import Image from "next/image";

const UploadBox = ({ dataUrl, setImages }) => {
  const [localImages, setLocalImages] = .useState([]);

  // Handle new image uploads
  const handleUpload = (e) => {
    const files = Array.from(e.target.files);
    const newImages = files.map((file) => ({
      id: crypto.randomUUID(),
      imageName: file.name,
      url: dataUrl || URL.createObjectURL(file),
    }));
    const updatedImages = [...localImages, ...newImages];
    setLocalImages(updatedImages);
    setImages(updatedImages); // Update parent with new array
  };

  // Handle image deletion
  const handleDelete = (index) => {
    const updatedImages = localImages.filter((_, i) => i !== index);
    setLocalImages(updatedImages);
    setImages(updatedImages); // Update parent
  };

  // Handle image replacement
  const handleReplace = (index, file) => {
    const newImage = {
      id: crypto.randomUUID(),
      imageName: file.name,
      url: dataUrl || URL.createObjectURL(file),
    };
    const updatedImages = [...localImages];
    updatedImages[index] = newImage;
    setLocalImages(updatedImages);
    setImages(updatedImages); // Update parent
  };

  return (
    <div className="max-w-2xl w-full bg-white p-6 rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4 text-center">Image Upload Box</h2>

      <div className="mb-6">
        <label className="block mb-2 text-lg font-medium">Select Images</label>
        <input
          type="file"
          multiple
          accept="image/*"
          onChange={handleUpload}
          className="w-full p-2 border rounded"
        />
      </div>

      <div className="mb-6">
        {localImages.length > 0 ? (
          localImages.map((image, index) => (
            <div
              key={image.id}
              className="flex items-center space-x-4 p-4 bg-gray-100 rounded-lg mb-4"
            >
              <span className="text-lg font-semibold">
                {index + 1} (ID: {image.id})
              </span>
              <Image
                width={1000}
                height={1000}
                src={image.url}
                alt={image.imageName}
                className="w-24 h-24 object-cover rounded"
              />
              <span className="text-sm truncate max-w-xs">
                {image.imageName}
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleDelete(index)}
                  className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                >
                  Delete
                </button>
                <label className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 cursor-pointer">
                  Replace
                  <input
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) =>
                      e.target.files[0] &&
                      handleReplace(index, e.target.files[0])
                    }
                  />
                </label>
              </div>
            </div>
          ))
        ) : (
          <p className="text-gray-500 text-center">No images uploaded yet.</p>
        )}
      </div>
    </div>
  );
};

export default UploadBox;
