"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/hooks/useGetUser";
import { createUser, handleGoogleLogin } from "@/lib/auth";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { FcGoogle } from "react-icons/fc";

type Inputs = {
  name?: string;
  email: string;
  password: string;
};

const RegisterPage = () => {
  const { user, loading } = useAuth({ redirectIfUnauthenticated: false });
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<Inputs>();

  const [formLoading, setFormLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const onSubmit: SubmitHandler<Inputs> = async (data) => {
    setFormLoading(true);
    setErrorMessage(null);
    const result = await createUser(data.email, data.password);
    setFormLoading(false);

    if ("user" in result) {
      console.log(result.user);
    } else {
      setErrorMessage(result.message || "Registration failed");
    }
  };

  // Handle Google sign-in
  const onGoogleLogin = async () => {
    setFormLoading(true);
    setErrorMessage(null);
    const result = await handleGoogleLogin();
    setFormLoading(false);

    if (result.user) {
      router.push("/profile"); // Redirect to profile after Google sign-in
    } else if (result.error) {
      setErrorMessage(result.error.message);
    }
  };

  if (loading) return <p>Loading...</p>;
  if (user) {
    router.push("/profile"); // Redirect to profile if already authenticated
    return null;
  }

  return (
    <div className="py-20 px-5">
      <form
        className="max-w-xl mx-auto flex flex-col gap-5 border p-10 rounded-md shadow-md"
        onSubmit={handleSubmit(onSubmit)}
      >
        <h2 className="text-4xl text-center uppercase">Register</h2>

        {/* Optional Name Field */}
        <div>
          <Label htmlFor="name" className="py-2 block">
            Name (Optional)
          </Label>
          <Input
            placeholder="Your Name"
            {...register("name")}
            className="border border-gray-300 rounded-md py-5 px-3 w-full"
            disabled={formLoading}
          />
        </div>

        {/* Email Field */}
        <div>
          <Label htmlFor="email" className="py-2 block">
            Email
          </Label>
          <Input
            placeholder="Email"
            {...register("email")}
            className="border border-gray-300 rounded-md py-5 px-3 w-full"
            disabled={formLoading}
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
          )}
        </div>

        {/* Password Field */}
        <div>
          <Label htmlFor="password" className="py-2 block">
            Password
          </Label>
          <Input
            type="password"
            placeholder="Password"
            {...register("password")}
            className="border border-gray-300 rounded-md py-5 px-3 w-full"
            disabled={formLoading}
          />
          {errors.password && (
            <p className="text-red-500 text-sm mt-1">
              {errors.password.message}
            </p>
          )}
        </div>

        {/* Links */}
        <div className="flex items-center justify-between">
          <Link href="/login">
            <span className="underline underline-offset-2 hover:font-semibold text-sm">
              Already have an account? Login
            </span>
          </Link>
          <Link href="/forget-password" className="text-sm text-slate-500">
            Forgot Password?
          </Link>
        </div>

        {/* Register Button */}
        <div>
          <button
            type="submit"
            className="bg-black w-full text-white rounded-md py-3 px-5 disabled:opacity-50"
            disabled={formLoading}
          >
            {formLoading ? "Registering..." : "Register"}
          </button>
          {errorMessage && (
            <p className="text-red-500 text-sm mt-2">{errorMessage}</p>
          )}
        </div>

        {/* Divider */}
        <div className="flex items-center gap-10">
          <hr className="w-full bg-slate-200 h-0.5 border-none" />
          <span>Or</span>
          <hr className="w-full bg-slate-200 h-0.5 border-none" />
        </div>

        {/* Google Sign-In Button */}
        <div>
          <button
            type="button"
            onClick={onGoogleLogin}
            className="bg-white flex justify-center items-center gap-5 font-semibold w-full text-black rounded-md py-3 px-5 border border-black disabled:opacity-50"
            disabled={formLoading}
          >
            <FcGoogle className="size-7" />
            {formLoading ? "Signing in..." : "Sign Up with Google"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default RegisterPage;
