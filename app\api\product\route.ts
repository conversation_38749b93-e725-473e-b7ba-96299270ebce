/* eslint-disable @typescript-eslint/no-explicit-any */
import connectToDatabase from "@/database/db.config";
import Product from "@/database/Schema/Product/AddProductModal";
import mongoose from "mongoose";
import { NextRequest, NextResponse } from "next/server";

// Helper function to handle errors
const handleError = (error: any, message: string) => {
  console.error(`${message}:`, error);
  return NextResponse.json(
    {
      success: false,
      message,
      error: error.message || "Unknown error occurred",
    },
    { status: 500 }
  );
};

// Helper function to validate ObjectId
const isValidObjectId = (id: string) => {
  return mongoose.Types.ObjectId.isValid(id);
};

// GET - Get all products or get product by ID
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const productId = searchParams.get("id");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";
    const isSale = searchParams.get("isSale");
    const isHaveCoupon = searchParams.get("isHaveCoupon");
    const search = searchParams.get("search");

    // Get single product by ID
    if (productId) {
      if (!isValidObjectId(productId)) {
        return NextResponse.json(
          {
            success: false,
            message: "Invalid product ID format",
          },
          { status: 400 }
        );
      }

      const product = await Product.findById(productId);

      if (!product) {
        return NextResponse.json(
          {
            success: false,
            message: "Product not found",
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        message: "Product retrieved successfully",
        data: product,
      });
    }

    // Build query for filtering
    const query: any = {};

    if (isSale !== null && isSale !== undefined) {
      query.isSale = isSale === "true";
    }

    if (isHaveCoupon !== null && isHaveCoupon !== undefined) {
      query.isHaveCoupon = isHaveCoupon === "true";
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { shortDescription: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;

    // Get products with pagination
    const products = await Product.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const totalProducts = await Product.countDocuments(query);
    const totalPages = Math.ceil(totalProducts / limit);

    return NextResponse.json({
      success: true,
      message: "Products retrieved successfully",
      data: products,
      pagination: {
        currentPage: page,
        totalPages,
        totalProducts,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    return handleError(error, "Failed to retrieve products");
  }
}

// POST - Create new product
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    const body = await request.json();
    console.log("Received product data:", JSON.stringify(body, null, 2));

    // Validate required fields
    const requiredFields = [
      "name",
      "shortDescription",
      "description",
      "price",
      "salePrice",
      "imgUrl",
      "descriptionImages",
    ];
    const missingFields = requiredFields.filter((field) => !body[field]);

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: `Missing required fields: ${missingFields.join(", ")}`,
        },
        { status: 400 }
      );
    }

    // Validate price logic
    if (body.salePrice > body.price) {
      return NextResponse.json(
        {
          success: false,
          message: "Sale price cannot be greater than regular price",
        },
        { status: 400 }
      );
    }

    // Validate coupon logic
    if (body.isHaveCoupon && !body.couponCode) {
      return NextResponse.json(
        {
          success: false,
          message: "Coupon code is required when product has coupon",
        },
        { status: 400 }
      );
    }

    // Validate image URLs array
    if (!Array.isArray(body.imgUrl) || body.imgUrl.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: "At least one product image is required",
        },
        { status: 400 }
      );
    }

    // Validate description images
    if (!body.descriptionImages.image1 || !body.descriptionImages.image2) {
      return NextResponse.json(
        {
          success: false,
          message: "Both description images are required",
        },
        { status: 400 }
      );
    }

    // Create new product
    const newProduct = new Product({
      name: body.name,
      shortDescription: body.shortDescription,
      description: body.description,
      price: parseFloat(body.price),
      salePrice: parseFloat(body.salePrice),
      imgUrl: body.imgUrl,
      descriptionImages: body.descriptionImages,
      isSale: Boolean(body.isSale),
      isHaveCoupon: Boolean(body.isHaveCoupon),
      couponCode: body.couponCode || undefined,
    });

    const savedProduct = await newProduct.save();

    return NextResponse.json(
      {
        success: true,
        message: "Product created successfully",
        data: savedProduct,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Product creation error:", error);

    // Handle mongoose validation errors
    if ((error as any).name === "ValidationError") {
      const validationErrors = Object.values((error as any).errors).map(
        (err: any) => err.message
      );
      console.error("Validation errors:", validationErrors);
      return NextResponse.json(
        {
          success: false,
          message: "Validation failed",
          errors: validationErrors,
        },
        { status: 400 }
      );
    }

    return handleError(error, "Failed to create product");
  }
}

// PUT - Update product by ID
export async function PUT(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const productId = searchParams.get("id");

    if (!productId) {
      return NextResponse.json(
        {
          success: false,
          message: "Product ID is required",
        },
        { status: 400 }
      );
    }

    if (!isValidObjectId(productId)) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid product ID format",
        },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Validate price logic if both prices are provided
    if (body.price && body.salePrice && body.salePrice > body.price) {
      return NextResponse.json(
        {
          success: false,
          message: "Sale price cannot be greater than regular price",
        },
        { status: 400 }
      );
    }

    // Validate coupon logic
    if (body.isHaveCoupon && !body.couponCode) {
      return NextResponse.json(
        {
          success: false,
          message: "Coupon code is required when product has coupon",
        },
        { status: 400 }
      );
    }

    // Validate image URLs array if provided
    if (
      body.imgUrl &&
      (!Array.isArray(body.imgUrl) || body.imgUrl.length === 0)
    ) {
      return NextResponse.json(
        {
          success: false,
          message: "At least one product image is required",
        },
        { status: 400 }
      );
    }

    // Validate description images if provided
    if (
      body.descriptionImages &&
      (!body.descriptionImages.image1 || !body.descriptionImages.image2)
    ) {
      return NextResponse.json(
        {
          success: false,
          message: "Both description images are required",
        },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    if (body.name) updateData.name = body.name;
    if (body.shortDescription)
      updateData.shortDescription = body.shortDescription;
    if (body.description) updateData.description = body.description;
    if (body.price) updateData.price = parseFloat(body.price);
    if (body.salePrice) updateData.salePrice = parseFloat(body.salePrice);
    if (body.imgUrl) updateData.imgUrl = body.imgUrl;
    if (body.descriptionImages)
      updateData.descriptionImages = body.descriptionImages;
    if (body.isSale !== undefined) updateData.isSale = Boolean(body.isSale);
    if (body.isHaveCoupon !== undefined)
      updateData.isHaveCoupon = Boolean(body.isHaveCoupon);
    if (body.couponCode !== undefined)
      updateData.couponCode = body.couponCode || undefined;

    const updatedProduct = await Product.findByIdAndUpdate(
      productId,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedProduct) {
      return NextResponse.json(
        {
          success: false,
          message: "Product not found",
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Product updated successfully",
      data: updatedProduct,
    });
  } catch (error) {
    // Handle mongoose validation errors
    if ((error as any).name === "ValidationError") {
      const validationErrors = Object.values((error as any).errors).map(
        (err: any) => err.message
      );
      return NextResponse.json(
        {
          success: false,
          message: "Validation failed",
          errors: validationErrors,
        },
        { status: 400 }
      );
    }

    return handleError(error, "Failed to update product");
  }
}

// DELETE - Delete product by ID or delete all products
export async function DELETE(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const productId = searchParams.get("id");
    const deleteAll = searchParams.get("deleteAll");

    // Delete all products (use with caution)
    if (deleteAll === "true") {
      const result = await Product.deleteMany({});

      return NextResponse.json({
        success: true,
        message: `Successfully deleted ${result.deletedCount} products`,
        deletedCount: result.deletedCount,
      });
    }

    // Delete single product by ID
    if (!productId) {
      return NextResponse.json(
        {
          success: false,
          message: "Product ID is required",
        },
        { status: 400 }
      );
    }

    if (!isValidObjectId(productId)) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid product ID format",
        },
        { status: 400 }
      );
    }

    const deletedProduct = await Product.findByIdAndDelete(productId);

    if (!deletedProduct) {
      return NextResponse.json(
        {
          success: false,
          message: "Product not found",
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Product deleted successfully",
      data: deletedProduct,
    });
  } catch (error) {
    return handleError(error, "Failed to delete product");
  }
}

// PATCH - Bulk operations (optional)
export async function PATCH(request: NextRequest) {
  try {
    await connectToDatabase();

    const body = await request.json();
    const { operation, productIds, updateData } = body;

    if (!operation) {
      return NextResponse.json(
        {
          success: false,
          message: "Operation type is required",
        },
        { status: 400 }
      );
    }

    switch (operation) {
      case "bulkUpdate":
        if (!productIds || !Array.isArray(productIds) || !updateData) {
          return NextResponse.json(
            {
              success: false,
              message:
                "Product IDs array and update data are required for bulk update",
            },
            { status: 400 }
          );
        }

        const bulkUpdateResult = await Product.updateMany(
          { _id: { $in: productIds } },
          updateData,
          { runValidators: true }
        );

        return NextResponse.json({
          success: true,
          message: `Successfully updated ${bulkUpdateResult.modifiedCount} products`,
          modifiedCount: bulkUpdateResult.modifiedCount,
        });

      case "bulkDelete":
        if (!productIds || !Array.isArray(productIds)) {
          return NextResponse.json(
            {
              success: false,
              message: "Product IDs array is required for bulk delete",
            },
            { status: 400 }
          );
        }

        const bulkDeleteResult = await Product.deleteMany({
          _id: { $in: productIds },
        });

        return NextResponse.json({
          success: true,
          message: `Successfully deleted ${bulkDeleteResult.deletedCount} products`,
          deletedCount: bulkDeleteResult.deletedCount,
        });

      case "toggleSale":
        if (!productIds || !Array.isArray(productIds)) {
          return NextResponse.json(
            {
              success: false,
              message: "Product IDs array is required for toggle sale",
            },
            { status: 400 }
          );
        }

        // Get current products to toggle their sale status
        const products = await Product.find({ _id: { $in: productIds } });
        const togglePromises = products.map((product) =>
          Product.findByIdAndUpdate(
            product._id,
            { isSale: !product.isSale },
            { new: true, runValidators: true }
          )
        );

        const toggleResults = await Promise.all(togglePromises);

        return NextResponse.json({
          success: true,
          message: `Successfully toggled sale status for ${toggleResults.length} products`,
          data: toggleResults,
        });

      default:
        return NextResponse.json(
          {
            success: false,
            message: "Invalid operation type",
          },
          { status: 400 }
        );
    }
  } catch (error) {
    return handleError(error, "Failed to perform bulk operation");
  }
}
