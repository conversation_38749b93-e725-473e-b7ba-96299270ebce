"use client";
import HeroSection from "@/components/Hero/HeroSection";
import PaginationComp from "@/components/pagination/Pagination";
import ProductCard from "@/components/ProductCard/ProductCard";
import WatchFilters from "@/components/ProductCategory/ProductFilterV2";
import useSlug from "@/hooks/useSlug";
import { useState } from "react";

const AllProducts = ({ params }: { params: string }) => {
  const itemsPerPage = 6;
  const totalProducts = 300;
  const [currentPage, setCurrentPage] = useState(1);

  // Paginate products based on current page
  const paginatedProducts = Array.from({ length: totalProducts })
    .map((_, index) => `Product ${index + 1}`)
    .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);
  const { fromSlug } = useSlug();
  return (
    <>
      <section className="mb-14">
        <HeroSection />
        <div className="mt-10 flex justify-between items-center px-5 lg:px-10">
          <h2>{fromSlug(params)}</h2>
          <button>Filter</button>
        </div>
      </section>
      <section className="min-h-screen h-max pb-20  px-5 lg:px-10 ">
        <div className="grid grid-cols-1 xl:grid-cols-12 gap-10">
          <div className="hidden lg:block lg:col-span-3 xl:col-span-3">
            <WatchFilters />
          </div>
          <div className="lg:col-span-9 xl:col-span-9">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 ">
              {paginatedProducts.map((item, index) => (
                <div key={item + index}>
                  <ProductCard />
                </div>
              ))}
            </div>
            <div className=" flex justify-center py-14">
              <PaginationComp
                totalItems={totalProducts}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
              />
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default AllProducts;
