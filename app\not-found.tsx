/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { gsap } from "gsap";
import Link from "next/link";
import { useEffect, useRef } from "react";

export default function NotFound() {
  const clockRef = useRef<HTMLDivElement>(null);
  const pointerRef = useRef<HTMLDivElement>(null);
  const starRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initial fade in
    gsap.fromTo(
      containerRef.current,
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" }
    );

    // Clock container fade in
    gsap.fromTo(
      clockRef.current,
      { scale: 0.8, opacity: 0 },
      { scale: 1, opacity: 1, duration: 1, delay: 0.3, ease: "power2.out" }
    );

    // Create a timeline for synchronized pointer and star rotation
    const tl = gsap.timeline({
      repeat: -1,
      defaults: { ease: "none" },
    });

    // Pointer continuous rotation
    tl.to(pointerRef.current, {
      rotation: 360,
      duration: 4,
      ease: "none",
    });

    // Star follows pointer
    gsap.to(starRef.current, {
      rotation: 360,
      duration: 4,
      repeat: -1,
      ease: "none",
    });

    // Star twinkle effect
    gsap.to(starRef.current, {
      scale: 1.5,
      opacity: 0.5,
      duration: 0.8,
      repeat: -1,
      yoyo: true,
      ease: "power1.inOut",
    });

    return () => {
      gsap.killTweensOf([
        containerRef.current,
        clockRef.current,
        pointerRef.current,
        starRef.current,
      ]);
    };
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-[#f8f9fa] p-4">
      <div ref={containerRef} className="text-center max-w-7xl mx-auto">
        <h2 className=" text-[#4a5568]  uppercase mb-10">
          <span className="text-4xl md:text-6xl block lg:text-[100px]  2xl:text-[120px] font-bold bg-gradient-to-b  text-transparent from-indigo-800 bg-clip-text via-black to-indigo-800">
            {" "}
            Oops!{" "}
          </span>
          <br />{" "}
          <span className="text-2xl md:text-4xl lg:text-6xl text-[#4a5568] mb-20 uppercase">
            Time got away from us.
          </span>
        </h2>

        <div ref={clockRef} className="relative w-48 h-48 mx-auto mb-20">
          <div className="absolute inset-0 rounded-full bg-white shadow-[0_4px_20px_rgba(0,0,0,0.08)]">
            {/* Pointer */}
            <div
              ref={pointerRef}
              className="absolute top-1/2 left-1/2 w-[1px] h-[45%] bg-black origin-bottom"
              style={{ transform: "translate(-50%, -100%)" }}
            />

            {/* Star */}
            <div
              ref={starRef}
              className="absolute w-3 h-3 left-1/2"
              style={{
                top: "calc(100% - 48px)",
                transform: "translateX(-50%)",
                transformOrigin: "50% 24px", // Half of the distance to center (48px)
              }}
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 12 12"
                fill="none"
                className="transform -translate-y-1/2"
              >
                <path
                  d="M6 0L7.5 4.5L12 6L7.5 7.5L6 12L4.5 7.5L0 6L4.5 4.5L6 0Z"
                  fill="black"
                  fillOpacity="0.2"
                />
              </svg>
            </div>
          </div>
        </div>

        <Link
          href="/"
          className="inline-block bg-[#171521] hover:bg-[#3182ce] text-white font-semibold py-3 px-8 rounded-md transition-all duration-300 ease-in-out transform hover:-translate-y-0.5 hover:shadow-lg z-50"
        >
          Return to Homepage
        </Link>
      </div>
    </div>
  );
}
