/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import gsap from "gsap";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useRef } from "react";
import "swiper/css";
import "swiper/css/navigation";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import ProductCard from "./ProductCard";
const ProductSection = ({ title }: { title: string }) => {
  const swiperRef = useRef(null);
  useEffect(() => {
    if (swiperRef.current && "swiper" in swiperRef.current) {
      (swiperRef.current as any).swiper.update();
      const onSlideChange = () => {
        gsap.fromTo(
          ".swiper-slide-active",
          { opacity: 0, scale: 0.9 },
          { opacity: 1, scale: 1, duration: 0.5, ease: "power2.out" }
        );
      };

      (swiperRef.current as any).swiper.on(
        "slideChangeTransitionStart",
        onSlideChange
      );

      return () => {
        (swiperRef.current as any).swiper?.off(
          "slideChangeTransitionStart",
          onSlideChange
        );
      };
    }
  }, []);
  return (
    <div>
      <div className="flex justify-between items-center gap-2 mb-10">
        <h2 className="text-2xl md:text-4xl lg:text-5xl font-semibold">
          {title}
        </h2>
        <div className="flex items-center gap-2">
          <button
            id="prev-button"
            className=" text-gray-900 size-[50px] flex justify-center items-center rounded-full shadow-xl"
          >
            <ChevronLeft size={24} />
          </button>
          <button
            id="next-button"
            className=" text-gray-900 size-[50px] flex justify-center items-center rounded-full shadow-xl"
          >
            <ChevronRight size={24} />
          </button>
        </div>
      </div>
      <div className="h-auto w-full ">
        <Swiper
          ref={swiperRef}
          spaceBetween={10}
          speed={1000}
          slidesPerView={1}
          loop={true}
          modules={[Navigation]}
          navigation={{
            prevEl: "#prev-button",
            nextEl: "#next-button",
          }}
          breakpoints={{
            640: {
              slidesPerView: 2,
              spaceBetween: 20,
            },
            768: {
              slidesPerView: 2,
              spaceBetween: 30,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 30,
            },
            1280: {
              slidesPerView: 4,
              spaceBetween: 20,
            },
          }}
        >
          {Array.from({ length: 30 }).map((_, index) => (
            <SwiperSlide key={index}>
              <ProductCard />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default ProductSection;
