import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface Product {
  _id: string;
  name: string;
  price: number;
  salePrice: number;
  quantity: number;
  imgUrl: Array<{ url: string }>;
  description?: string;
  brand?: string;
  category?: string;
}

interface CartState {
  items: Product[];
  totalAmount: number;
  totalQuantity: number;
}

// Load cart from localStorage
const loadCartFromStorage = (): CartState => {
  if (typeof window !== "undefined") {
    try {
      const savedCart = localStorage.getItem("cart");
      if (savedCart) {
        return JSON.parse(savedCart);
      }
    } catch (error) {
      console.error("Error loading cart from localStorage:", error);
    }
  }
  return {
    items: [],
    totalAmount: 0,
    totalQuantity: 0,
  };
};

// Save cart to localStorage
const saveCartToStorage = (state: CartState) => {
  if (typeof window !== "undefined") {
    try {
      localStorage.setItem("cart", JSON.stringify(state));
    } catch (error) {
      console.error("Error saving cart to localStorage:", error);
    }
  }
};

const initialState: CartState = loadCartFromStorage();

const cartSlice = createSlice({
  name: "cart",
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<Product>) => {
      const newItem = action.payload;
      const existingItem = state.items.find((item) => item._id === newItem._id);

      if (!existingItem) {
        state.items.push({
          ...newItem,
          quantity: 1,
        });
        state.totalQuantity++;
        state.totalAmount += newItem.salePrice;
      } else {
        existingItem.quantity++;
        state.totalQuantity++;
        state.totalAmount += newItem.salePrice;
      }

      saveCartToStorage(state);
    },

    removeFromCart: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      const existingItem = state.items.find((item) => item._id === id);

      if (existingItem) {
        state.totalQuantity -= existingItem.quantity;
        state.totalAmount -= existingItem.salePrice * existingItem.quantity;
        state.items = state.items.filter((item) => item._id !== id);
      }

      saveCartToStorage(state);
    },

    updateQuantity: (
      state,
      action: PayloadAction<{ id: string; quantity: number }>
    ) => {
      const { id, quantity } = action.payload;
      const item = state.items.find((item) => item._id === id);

      if (item && quantity > 0) {
        const quantityDifference = quantity - item.quantity;
        item.quantity = quantity;
        state.totalQuantity += quantityDifference;
        state.totalAmount += item.salePrice * quantityDifference;
      }

      saveCartToStorage(state);
    },

    clearCart: (state) => {
      state.items = [];
      state.totalAmount = 0;
      state.totalQuantity = 0;
      saveCartToStorage(state);
    },
  },
});

const cartReducer = cartSlice.reducer;
export const { addToCart, removeFromCart, updateQuantity, clearCart } =
  cartSlice.actions;
export default cartReducer;
