"use client";

import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { ChevronDown, ChevronUp, X } from "lucide-react";
import { useCallback, useState } from "react";

interface FilterOption {
  [key: string]: string[];
}

const filterOptions: FilterOption = {
  Gender: ["Men", "Women", "Unisex"],
  Brand: ["Rolex", "Omega", "Seiko", "Casio", "Fossil"],
  Model: ["Submariner", "Speedmaster", "G-Shock", "Datejust"],
  "Product Type": ["Analog", "Digital", "Smart Watch", "Chronograph"],
  "Price (₹)": [
    "Under ₹5,000",
    "₹5,000 - ₹10,000",
    "₹10,000 - ₹50,000",
    "Above ₹50,000",
  ],
  "Case Size": ["Small (< 38mm)", "Medium (38-42mm)", "Large (> 42mm)"],
  "Dial Color": ["Black", "White", "Blue", "Silver", "Gold"],
  "Strap Color": ["Black", "Brown", "Silver", "Gold", "Blue"],
  "Percent Sale": ["10% Off", "20% Off", "30% Off", "40% Off", "50% & Above"],
  "Dial shape": ["Round", "Square", "Rectangular", "Oval"],
  Movement: ["Quartz", "Automatic", "Mechanical", "Solar"],
};

interface FilterState {
  [key: string]: string[];
}

interface FilterSectionProps {
  title: string;
  options: string[];
  selected: string[];
  onToggle: (option: string) => void;
  isOpen: boolean;
  onSectionToggle: () => void;
}

function FilterSection({
  title,
  options,
  selected,
  onToggle,
  isOpen,
  onSectionToggle,
}: FilterSectionProps) {
  return (
    <div className="border-b">
      <button
        onClick={onSectionToggle}
        className="flex w-full items-center justify-between py-3 text-sm"
      >
        {title}
        {isOpen ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </button>
      <div
        className={cn(
          "overflow-hidden transition-all duration-200",
          isOpen ? "max-h-96 pb-3" : "max-h-0"
        )}
      >
        <div className="space-y-2">
          {options.map((option) => (
            <div key={option} className="flex items-center space-x-2">
              <Checkbox
                id={`${title}-${option}`}
                checked={selected.includes(option)}
                onCheckedChange={() => onToggle(option)}
              />
              <Label
                htmlFor={`${title}-${option}`}
                className="text-sm font-normal"
              >
                {option}
              </Label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default function WatchFilters() {
  const [openSections, setOpenSections] = useState<string[]>([]);
  const [selectedFilters, setSelectedFilters] = useState<FilterState>({});

  const toggleSection = (section: string) => {
    setOpenSections((current) =>
      current.includes(section)
        ? current.filter((s) => s !== section)
        : [...current, section]
    );
  };

  const toggleFilter = useCallback((category: string, option: string) => {
    setSelectedFilters((current) => {
      const currentCategory = current[category] || [];
      const updated = currentCategory.includes(option)
        ? currentCategory.filter((item) => item !== option)
        : [...currentCategory, option];

      return {
        ...current,
        [category]: updated,
      };
    });
  }, []);

  const removeFilter = useCallback((category: string, option: string) => {
    setSelectedFilters((current) => ({
      ...current,
      [category]: current[category].filter((item) => item !== option),
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setSelectedFilters({});
  }, []);

  const getSelectedCount = () => {
    return Object.values(selectedFilters).reduce(
      (acc, curr) => acc + curr.length,
      0
    );
  };

  return (
    <form
      onSubmit={(e) => e.preventDefault()}
      className="w-full  border p-5 bg-white"
    >
      <div className="flex items-center justify-between pb-4 border-b">
        <h2 className="font-medium">Filters</h2>
        {getSelectedCount() > 0 && (
          <button
            type="button"
            onClick={resetFilters}
            className="text-sm text-gray-500 hover:text-gray-900"
          >
            Reset all
          </button>
        )}
      </div>

      {getSelectedCount() > 0 && (
        <div className="py-3 border-b flex flex-wrap gap-2">
          {Object.entries(selectedFilters).map(([category, options]) =>
            options.map((option) => (
              <Badge
                key={`${category}-${option}`}
                variant="secondary"
                className="flex items-center gap-1"
              >
                {option}
                <button
                  type="button"
                  onClick={() => removeFilter(category, option)}
                  className="ml-1 hover:text-gray-900"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))
          )}
        </div>
      )}

      {Object.entries(filterOptions).map(([category, options]) => (
        <FilterSection
          key={category}
          title={category}
          options={options}
          selected={selectedFilters[category] || []}
          onToggle={(option) => toggleFilter(category, option)}
          isOpen={openSections.includes(category)}
          onSectionToggle={() => toggleSection(category)}
        />
      ))}
    </form>
  );
}
