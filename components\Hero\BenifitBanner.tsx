import { CreditCard, ShoppingCart, Store, Target, Wallet } from "lucide-react";

const BenefitsBanner = () => {
  const benefits = [
    {
      icon: <Target className="w-8 h-8 md:w-10 md:h-10" />,
      text: "No Cost EMI above 7k",
    },
    {
      icon: <Wallet className="w-8 h-8 md:w-10 md:h-10" />,
      text: "COD available upto 25k",
    },
    {
      icon: <CreditCard className="w-8 h-8 md:w-10 md:h-10" />,
      text: "10% off on 1st purchase - WELCOME10",
    },
    {
      icon: <ShoppingCart className="w-8 h-8 md:w-10 md:h-10" />,
      text: "Ship in 24 hours",
    },
    {
      icon: <Store className="w-8 h-8 md:w-10 md:h-10" />,
      text: "1 yr Warranty",
    },
  ];

  return (
    <div className="w-full bg-black text-white py-16 ">
      <div className="container mx-auto px-4">
        <div className="flex flex-wrap justify-center md:justify-between items-center gap-4 md:gap-2">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="flex flex-col items-center text-center w-1/2 sm:w-1/3 md:w-auto"
            >
              <div className="mb-2">{benefit.icon}</div>
              <p className="text-xs sm:text-sm md:text-base lg:text-xl uppercase font-medium  whitespace-normal px-2">
                {benefit.text}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BenefitsBanner;
