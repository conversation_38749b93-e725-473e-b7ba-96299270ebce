/* eslint-disable @next/next/no-img-element */
"use client";
import { addToCart } from "@/Store/Features/addToCardSlice";
import { useAppDispatch } from "@/Store/hooks/reduxHook";
import gsap from "gsap";
import { useEffect, useRef, useState } from "react";
import { BiChevronLeft, BiChevronRight } from "react-icons/bi";
import { FaHeart, FaRegHeart } from "react-icons/fa";
import { FaStar } from "react-icons/fa6";
import CountDown from "../CountDown/CountDown";
import { Card } from "../ui/card";

// Product interface
interface Product {
  _id: string;
  name: string;
  shortDescription: string;
  description: string;
  price: number;
  salePrice: number;
  imgUrl: Array<{
    id: string;
    imageName: string;
    url: string;
  }>;
  descriptionImages: {
    image1: string;
    image2: string;
  };
  brand?: string;
  category?: string;
  isSale: boolean;
  isNew?: boolean;
  isTrending?: boolean;
  isHaveCoupon: boolean;
  couponCode?: string;
  stock?: number;
  createdAt: string;
  updatedAt: string;
}

interface ProductDetailsCardProps {
  product: Product;
}

const ProductDetailsCard = ({ product }: ProductDetailsCardProps) => {
  const dispatch = useAppDispatch();

  // Use product images or fallback to default
  const images =
    product.imgUrl && product.imgUrl.length > 0
      ? product.imgUrl.map((img) => img.url)
      : [
          "https://www.justwatches.com/cdn/shop/files/NAPNOS302.jpg?v=1693982626&width=3000",
          "https://www.justwatches.com/cdn/shop/files/NAPNOS302-2.jpg?v=1693982626&width=3000",
          "https://www.justwatches.com/cdn/shop/files/NAPNOS302-3.jpg?v=1693982626&width=3000",
        ];
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [imgDirection, setImageDirection] = useState("right");
  const imgRef = useRef<HTMLImageElement>(null);

  const [isFavorite, setIsFavorite] = useState(false);
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  useEffect(() => {
    if (imgRef.current) {
      if (imgDirection === "right") {
        gsap.fromTo(
          imgRef.current,
          {
            duration: 0.5,
            x: -1000,
            ease: "power1.inOut",
          },
          {
            x: 0,
          }
        );
      } else {
        gsap.fromTo(
          imgRef.current,
          {
            duration: 0.5,
            x: 1000,
            ease: "power1.inOut",
          },
          {
            x: 0,
            opacity: 1,
          }
        );
      }
    }
  }, [currentImageIndex, imgDirection]);

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
    setImageDirection("right");
  };

  const previousImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
    setImageDirection("left");
  };

  // Handle add to cart
  const handleAddToCart = () => {
    if (product.stock === 0) {
      alert("This product is out of stock");
      return;
    }

    dispatch(
      addToCart({
        ...product,
        quantity: quantity,
      })
    );

    alert(`${quantity} x ${product.name} added to cart!`);
  };

  return (
    <div>
      <div className="max-w-7xl mx-auto px-5 md:px-8 md:py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
          {/* Left side - Image gallery */}
          <div className="space-y-4">
            <div className="relative aspect-square">
              {/* NEW and SALE tags */}
              <div className="absolute top-4 left-4 z-10 space-y-2">
                {product.isNew && (
                  <span className="inline-block px-2 py-1 text-xs font-semibold bg-green-500 text-white rounded">
                    NEW
                  </span>
                )}
                {product.isSale && (
                  <div className="inline-block px-2 py-1 text-xs font-semibold bg-red-500 text-white rounded">
                    SALE
                  </div>
                )}
                {product.isTrending && (
                  <div className="inline-block px-2 py-1 text-xs font-semibold bg-blue-500 text-white rounded">
                    TRENDING
                  </div>
                )}
                {product.price > product.salePrice && (
                  <div className="inline-block px-2 py-1 text-xs font-semibold bg-emerald-500 text-white rounded">
                    -
                    {Math.round(
                      ((product.price - product.salePrice) / product.price) *
                        100
                    )}
                    %
                  </div>
                )}
              </div>

              {/* Main image with navigation arrows */}
              <div className="relative h-full overflow-hidden">
                <img
                  ref={imgRef}
                  src={images[currentImageIndex]}
                  alt={`Product view ${currentImageIndex + 1}`}
                  className="w-full h-full object-cover"
                />
                <button
                  onClick={previousImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white shadow-lg hover:bg-[#0FABCA] hover:text-white"
                  aria-label="Previous image"
                >
                  <BiChevronLeft className="w-6 h-6" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white shadow-lg hover:bg-[#0FABCA] hover:text-white"
                  aria-label="Next image"
                >
                  <BiChevronRight className="w-6 h-6" />
                </button>
              </div>
            </div>

            {/* Thumbnail images */}
            <div className="flex gap-4 justify-between">
              {images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`relative transition-all duration-300 w-[8rem] aspect-square ${
                    currentImageIndex === index
                      ? "ring-2 ring-[#0FABCA]"
                      : "hover:ring-2 hover:ring-[#0FABCA]"
                  }`}
                >
                  <img
                    src={image}
                    alt={`Thumbnail ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Right side - Product details */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <div className="flex gap-0.5">
                {[...Array(5)].map((_, i) => (
                  <FaStar key={i} className="w-4 h-4 fill-black" />
                ))}
              </div>
              <span className="text-sm text-gray-600">11 Reviews</span>
            </div>

            <h1 className="text-[1.6rem] md:text-[1.9rem] text-gray-800 font-semibold">
              {product.name}
            </h1>

            {/* Short Description */}
            <p className="text-gray-600 text-[0.9rem] leading-relaxed">
              {product.shortDescription ||
                "Premium quality product with excellent craftsmanship and attention to detail."}
            </p>

            {/* Expandable Long Description */}

            {product.brand && (
              <p className="text-sm text-gray-500 font-medium">
                Brand: <span className="text-gray-700">{product.brand}</span>
              </p>
            )}

            <div className="flex items-center gap-3">
              <span className="text-[1.5rem] text-green-600 font-medium">
                ৳{product.salePrice.toFixed(2)}
              </span>
              {product.price > product.salePrice && (
                <span className="text-lg text-gray-500 line-through">
                  ৳{product.price.toFixed(2)}
                </span>
              )}
            </div>

            {product.stock && (
              <p className="text-sm text-gray-600">
                <span className="font-medium">Stock:</span> {product.stock}{" "}
                available
              </p>
            )}

            <div>
              <CountDown />
            </div>

            <div className="flex gap-4 items-center pt-6">
              <div className="flex items-center bg-gray-100 rounded-md">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-4 py-[0.560rem] text-[1.3rem] font-[300] hover:bg-gray-100 rounded-l-md"
                >
                  −
                </button>
                <input
                  type="number"
                  value={quantity}
                  onChange={(e) =>
                    setQuantity(Math.max(1, parseInt(e.target.value) || 1))
                  }
                  className="w-10 font-medium outline-none text-[0.9rem] bg-transparent text-center"
                />
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-4 py-[0.560rem] text-[1.3rem] font-[300] hover:bg-gray-100 rounded-r-md"
                >
                  +
                </button>
              </div>
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className="py-3 border border-gray-200 rounded-md flex items-center justify-center gap-[10px] grow hover:bg-gray-50"
              >
                {isFavorite ? (
                  <FaHeart className="w-5 h-5 text-red-500" />
                ) : (
                  <FaRegHeart className="w-5 h-5 text-gray-800" />
                )}
                Wishlist
              </button>
            </div>
            <div className="my-4">
              <button
                onClick={handleAddToCart}
                disabled={product.stock === 0}
                className="w-full px-6 py-3 bg-[#0FABCA] text-white rounded-md hover:bg-[#0FABCA]/90 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {product.stock === 0 ? "Out of Stock" : "Add to Cart"}
              </button>
            </div>
          </div>
        </div>
        <div className="mt-8">
          <Card className="p-4">
            {product.description && (
              <div className="space-y-2">
                <div
                  className={`text-gray-600 text-sm leading-relaxed transition-all duration-300 ${
                    isDescriptionExpanded
                      ? "max-h-none"
                      : "max-h-20 overflow-hidden"
                  }`}
                  dangerouslySetInnerHTML={{ __html: product.description }}
                />
                <button
                  onClick={() =>
                    setIsDescriptionExpanded(!isDescriptionExpanded)
                  }
                  className="text-[#0FABCA] text-sm font-medium hover:underline transition-colors"
                >
                  {isDescriptionExpanded ? "Show Less" : "Show More"}
                </button>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsCard;
