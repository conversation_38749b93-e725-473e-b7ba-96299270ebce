/* eslint-disable @typescript-eslint/no-unused-expressions */
"use client";

import product1Image from "@/public/img-1.webp"; // Adjust paths to your product images
import product2Image from "@/public/img-2.webp"; // Adjust paths to your product images
import logoPng from "@/public/logo.png"; // Adjust the path to your PNG logo
import jsPDF from "jspdf"; // For PDF generation
import { useRouter } from "next/navigation";
import React from "react";

// Assuming these are your order details (you'd typically get these from props or context)
interface OrderDetails {
  firstName: string;
  lastName: string;
  company?: string;
  address: string;
  country: string;
  state: string;
  city: string;
  zipCode: string;
  email: string;
  phone: string;
  items: { name: string; price: number; quantity: number; image?: string }[];
  total: number;
}
const sampleOrder: OrderDetails = {
  firstName: "John",
  lastName: "Doe",
  company: "ABC Corp",
  address: "123 Main St",
  country: "USA",
  state: "California",
  city: "Los Angeles",
  zipCode: "90001",
  email: "<EMAIL>",
  phone: "************",
  items: [
    { name: "Product 1", price: 29.99, quantity: 2, image: "img-1.webp" },
    { name: "Product 2", price: 19.99, quantity: 1, image: "img-2.webp" },
  ],
  total: 79.97,
};

const ThankYouPage: React.FC = () => {
  const router = useRouter();

  const handleContinueShopping = () => {
    router.push("/"); // Adjust this route to your shop page
  };

  const generatePDF = () => {
    const doc = new jsPDF({
      unit: "mm",
      format: "a4",
    });

    const pageWidth = doc.internal.pageSize.getWidth();
    // const pageHeight = doc.internal.pageSize.getHeight();

    // Add logo above "Order Summary," resized to 120px x 120px (1 pixel ~ 0.264583 mm)
    const logoWidth = 120 * 0.264583; // ~31.75mm
    const logoHeight = 120 * 0.264583; // ~31.75mm
    const logoX = (pageWidth - logoWidth) / 2;
    const logoY = 10; // Position above "Order Summary" text
    doc.addImage(logoPng.src, "PNG", logoX, logoY, logoWidth, logoHeight);

    // Header
    doc.setFontSize(24);
    doc.text("Order Summary", pageWidth / 2, 50, { align: "center" }); // Moved down to accommodate logo
    doc.setFontSize(12);
    doc.text(`Date: ${new Date().toLocaleDateString()}`, pageWidth / 2, 60, {
      align: "center",
    });

    // From and To Information
    doc.setFontSize(14);
    doc.text("From", 20, 80);
    doc.setFontSize(12);
    doc.text("LumeAxis", 20, 90); // Replace with your company name
    doc.text("123 Business St, Suite 100, City, Country", 20, 100); // Replace with your address
    doc.text("**************", 20, 110); // Replace with your phone
    doc.text("<EMAIL>", 20, 120); // Replace with your email
    doc.text("www.lumeaxis.com/support", 20, 130); // Replace with your support URL

    doc.text("To", pageWidth - 20, 80, { align: "right" });
    doc.setFontSize(12);
    doc.text(
      `${sampleOrder.firstName} ${sampleOrder.lastName}`,
      pageWidth - 20,
      90,
      { align: "right" }
    );
    sampleOrder.company &&
      doc.text(`${sampleOrder.company}`, pageWidth - 20, 100, {
        align: "right",
      });
    doc.text(
      `${sampleOrder.address}, ${sampleOrder.city}, ${sampleOrder.state}, ${sampleOrder.country} ${sampleOrder.zipCode}`,
      pageWidth - 20,
      sampleOrder.company ? 110 : 100,
      { align: "right" }
    );
    doc.text(
      sampleOrder.email,
      pageWidth - 20,
      sampleOrder.company ? 120 : 110,
      { align: "right" }
    );
    doc.text(
      sampleOrder.phone,
      pageWidth - 20,
      sampleOrder.company ? 130 : 120,
      { align: "right" }
    );

    // Horizontal line
    doc.setLineWidth(0.5);
    doc.line(20, 140, pageWidth - 20, 140); // Moved line below From/To

    // Tabular format for order items
    doc.setFontSize(14);
    doc.text("Description", 20, 150);
    doc.text("Total", pageWidth - 20, 150, { align: "right" });

    let yPosition = 160;
    doc.setFontSize(12);

    // Add product images and details in a table-like format
    sampleOrder.items.forEach((item, index) => {
      // Add product image (80px x 80px, converted to mm: 1 pixel ~ 0.264583 mm)
      const imgWidth = 50 * 0.264583; // ~21.17mm
      const imgHeight = 50 * 0.264583; // ~21.17mm
      const imgX = 20;
      const imgY = yPosition;
      if (item.image) {
        doc.addImage(
          item.image === "img-1.webp" ? product1Image.src : product2Image.src,
          "WEBP", // Using WEBP format
          imgX,
          imgY,
          imgWidth,
          imgHeight
        );
      }

      // Product description
      doc.text(`Item ${index + 1}: ${item.name}`, 50, yPosition + 10); // Adjusted position for larger image
      // Total for each item
      doc.text(
        `$${item.price.toFixed(2)} x ${item.quantity}`,
        pageWidth - 20,
        yPosition + 12,
        { align: "right" }
      );
      yPosition += 20; // Adjusted spacing for larger image
    });

    // Totals (Subtotal, Tax, Total)
    const subtotal = sampleOrder.items.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );
    const tax = subtotal * 0.1; // 10% tax as in your example
    const finalTotal = sampleOrder.total;

    yPosition += 10;
    doc.setFontSize(14);
    doc.text("Subtotal", 20, yPosition);
    doc.text(`$${subtotal.toFixed(2)}`, pageWidth - 20, yPosition, {
      align: "right",
    });

    yPosition += 10;
    doc.text("Tax (10%)", 20, yPosition);
    doc.text(`$${tax.toFixed(2)}`, pageWidth - 20, yPosition, {
      align: "right",
    });

    yPosition += 10;
    doc.setFontSize(24); // Increased font size for total
    doc.setFont("helvetica", "bold");
    doc.setTextColor(255, 255, 255); // White text for contrast
    const totalText = `Total - $${finalTotal.toFixed(2)}`;
    const textWidth = doc.getTextWidth(totalText);
    const rectX = pageWidth - 20 - textWidth - 5; // Position rectangle to fit text on the right
    const rectWidth = textWidth + 10; // Add padding
    doc.rect(rectX, yPosition - 5, rectWidth, 10, "F"); // Black background rectangle for total, positioned on the right
    doc.text(totalText, pageWidth - 20, yPosition + 2, {
      align: "right",
    });

    // Download the PDF
    doc.save(`order-summary-${sampleOrder.firstName}-${Date.now()}.pdf`);
  };

  return (
    <div className="min-h-[50vh] flex items-center justify-center bg-gray-100 py-12 px-4">
      <div className="max-w-xl w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">
          Thank You for Your Purchase!
        </h1>
        <p className="text-gray-600 mb-8">
          Your order has been successfully processed. We appreciate your
          business!
        </p>

        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <button
            onClick={handleContinueShopping}
            className="bg-black hover:bg-transparent border-2  hover:border-gray-700 hover:text-black  text-white font-medium py-2 px-6 rounded-md transition-colors duration-200"
          >
            Continue Shopping
          </button>
          <button
            onClick={generatePDF}
            className="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-6 rounded-md transition-colors duration-200"
          >
            Download Order Summary
          </button>
        </div>
      </div>
    </div>
  );
};

export default ThankYouPage;
