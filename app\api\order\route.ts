import connectToDatabase from "@/database/db.config";
import Order from "@/database/Schema/Order/OrderModal";
import Product from "@/database/Schema/Product/AddProductModal";
import mongoose from "mongoose";
import { NextRequest, NextResponse } from "next/server";

// Helper function to handle errors
const handleError = (error: unknown, message: string) => {
  console.error(`${message}:`, error);
  const errorMessage =
    error instanceof Error ? error.message : "Unknown error occurred";
  return NextResponse.json(
    {
      success: false,
      message,
      error: errorMessage,
    },
    { status: 500 }
  );
};

// Helper function to validate ObjectId
const isValidObjectId = (id: string) => {
  return mongoose.Types.ObjectId.isValid(id);
};

// GET - Get all orders or get order by ID
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get("id");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const paymentStatus = searchParams.get("paymentStatus");
    const search = searchParams.get("search");
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // Get single order by ID
    if (orderId) {
      const order = await Order.findById(orderId);

      if (!order) {
        return NextResponse.json(
          {
            success: false,
            message: "Order not found",
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        message: "Order retrieved successfully",
        data: order,
      });
    }

    // Build query for filtering
    const query: Record<string, unknown> = {};

    if (status) {
      query.status = status;
    }

    if (paymentStatus) {
      query.paymentStatus = paymentStatus;
    }

    if (search) {
      query.$or = [
        { orderId: { $regex: search, $options: "i" } },
        { "customerInfo.name": { $regex: search, $options: "i" } },
        { "customerInfo.email": { $regex: search, $options: "i" } },
        { "customerInfo.phone": { $regex: search, $options: "i" } },
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build sort object
    const sort: Record<string, 1 | -1> = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;

    // Get orders with pagination
    const orders = await Order.find(query).sort(sort).skip(skip).limit(limit);

    // Get total count for pagination
    const totalOrders = await Order.countDocuments(query);
    const totalPages = Math.ceil(totalOrders / limit);

    // Get order statistics
    const stats = await Order.aggregate([
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
          totalAmount: { $sum: "$total" },
        },
      },
    ]);

    return NextResponse.json({
      success: true,
      message: "Orders retrieved successfully",
      data: orders,
      pagination: {
        currentPage: page,
        totalPages,
        totalOrders,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
      stats,
    });
  } catch (error) {
    return handleError(error, "Failed to retrieve orders");
  }
}

// POST - Create new order
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    const body = await request.json();

    // Validate required fields
    const requiredFields = ["customerInfo", "items", "paymentMethod"];
    const missingFields = requiredFields.filter((field) => !body[field]);

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: `Missing required fields: ${missingFields.join(", ")}`,
        },
        { status: 400 }
      );
    }

    // Validate customer info
    const { customerInfo } = body;
    if (
      !customerInfo.name ||
      !customerInfo.email ||
      !customerInfo.phone ||
      !customerInfo.address
    ) {
      return NextResponse.json(
        {
          success: false,
          message: "Complete customer information is required",
        },
        { status: 400 }
      );
    }

    // Validate items
    if (!Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: "Order must contain at least one item",
        },
        { status: 400 }
      );
    }

    // Validate and fetch product details
    const validatedItems = [];
    for (const item of body.items) {
      if (!item.productId || !item.quantity || item.quantity <= 0) {
        return NextResponse.json(
          {
            success: false,
            message: "Invalid item data",
          },
          { status: 400 }
        );
      }

      // Fetch product details
      const product = await Product.findById(item.productId);
      if (!product) {
        return NextResponse.json(
          {
            success: false,
            message: `Product not found: ${item.productId}`,
          },
          { status: 404 }
        );
      }

      // Calculate item total
      const itemPrice = product.isSale ? product.salePrice : product.price;
      const itemTotal = itemPrice * item.quantity;

      validatedItems.push({
        productId: product._id.toString(),
        productName: product.name,
        productImage: product.imgUrl[0]?.url || "",
        price: product.price,
        salePrice: product.salePrice,
        quantity: item.quantity,
        total: itemTotal,
      });
    }

    // Create new order
    const newOrder = new Order({
      customerId: body.customerId || undefined,
      customerInfo: {
        name: customerInfo.name,
        email: customerInfo.email,
        phone: customerInfo.phone,
        address: customerInfo.address,
      },
      items: validatedItems,
      tax: body.tax || 0,
      shipping: body.shipping || 0,
      discount: body.discount || 0,
      paymentMethod: body.paymentMethod,
      notes: body.notes || "",
    });

    const savedOrder = await newOrder.save();

    return NextResponse.json(
      {
        success: true,
        message: "Order created successfully",
        data: savedOrder,
      },
      { status: 201 }
    );
  } catch (error) {
    // Handle mongoose validation errors
    if (
      error &&
      typeof error === "object" &&
      "name" in error &&
      error.name === "ValidationError" &&
      "errors" in error
    ) {
      const mongooseError = error as unknown as {
        errors: Record<string, { message: string }>;
      };
      const validationErrors = Object.values(mongooseError.errors).map(
        (err) => err.message
      );
      return NextResponse.json(
        {
          success: false,
          message: "Validation failed",
          errors: validationErrors,
        },
        { status: 400 }
      );
    }

    return handleError(error, "Failed to create order");
  }
}

// PUT - Update order status
export async function PUT(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get("id");

    if (!orderId) {
      return NextResponse.json(
        {
          success: false,
          message: "Order ID is required",
        },
        { status: 400 }
      );
    }

    if (!isValidObjectId(orderId)) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid order ID format",
        },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Prepare update data
    const updateData: Record<string, unknown> = {};

    if (body.status) updateData.status = body.status;
    if (body.paymentStatus) updateData.paymentStatus = body.paymentStatus;
    if (body.trackingNumber) updateData.trackingNumber = body.trackingNumber;
    if (body.estimatedDelivery)
      updateData.estimatedDelivery = new Date(body.estimatedDelivery);
    if (body.notes !== undefined) updateData.notes = body.notes;

    const updatedOrder = await Order.findByIdAndUpdate(orderId, updateData, {
      new: true,
      runValidators: true,
    });

    if (!updatedOrder) {
      return NextResponse.json(
        {
          success: false,
          message: "Order not found",
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Order updated successfully",
      data: updatedOrder,
    });
  } catch (error) {
    return handleError(error, "Failed to update order");
  }
}

// DELETE - Delete order
export async function DELETE(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get("id");

    if (!orderId) {
      return NextResponse.json(
        {
          success: false,
          message: "Order ID is required",
        },
        { status: 400 }
      );
    }

    if (!isValidObjectId(orderId)) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid order ID format",
        },
        { status: 400 }
      );
    }

    const deletedOrder = await Order.findByIdAndDelete(orderId);

    if (!deletedOrder) {
      return NextResponse.json(
        {
          success: false,
          message: "Order not found",
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Order deleted successfully",
      data: deletedOrder,
    });
  } catch (error) {
    return handleError(error, "Failed to delete order");
  }
}
