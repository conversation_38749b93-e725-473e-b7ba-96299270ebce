// app/hooks/useAuth.ts
import { auth } from "@/lib/firebase";
import { User } from "firebase/auth";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export const useAuth = (options?: { redirectIfUnauthenticated?: boolean }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      setUser(currentUser);
      setLoading(false);

      // Only redirect if option is explicitly true and no user exists
      if (options?.redirectIfUnauthenticated && !currentUser) {
        router.push("/login");
      }
    });

    return () => unsubscribe();
  }, [router, options?.redirectIfUnauthenticated]);

  return { user, loading };
};
