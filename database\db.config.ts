import mongoose from "mongoose";

const MongoDbString =
  process.env.NODE_ENV === "production"
    ? process.env.MONGO_URL_ONLINE
    : process.env.MONGO_URL_ONLINE || "";

if (!MongoDbString) {
  throw new Error("Please define the MONGO_URL_ONLINE environment variable");
}

let isConnected: boolean = false;

export const connectToDatabase = async (): Promise<void> => {
  if (isConnected) {
    console.log("MongoDB is already connected");
    return;
  }

  try {
    const db = await mongoose.connect(MongoDbString, {
      dbName: "VendorDB",
      autoIndex: true,
    });

    isConnected = db.connections[0].readyState === 1;
    console.log("MongoDB connected successfully");
  } catch (error) {
    console.error("MongoDB connection failed:", error);
    throw new Error("Failed to connect to the database");
  }
};
