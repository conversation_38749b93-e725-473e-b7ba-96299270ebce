{"name": "vendor-full-stack-v1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.37.4", "@gsap/react": "^2.1.2", "@heroicons/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@types/fancybox": "^3.5.7", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "gsap": "^3.13.0", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "lenis": "^1.3.3", "lucide-react": "^0.475.0", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "next": "15.1.0", "next-auth": "^4.24.11", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.6", "primereact": "^10.9.5", "quill": "^2.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-fancybox": "^0.2.5", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "sonner": "^1.7.4", "swiper": "^11.2.8", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^20.17.50", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "eslint": "^9.27.0", "eslint-config-next": "15.1.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "pnpm": {"ignoredBuiltDependencies": ["core-js"], "onlyBuiltDependencies": ["core-js", "protobufjs"]}, "trustedDependencies": ["@firebase/util", "core-js", "protobufjs", "unrs-resolver"]}