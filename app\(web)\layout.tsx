import Footer from "@/components/footer/footer";
import LenisScroll from "@/components/Lenis/LenisScroll";
import Navbar from "@/components/Navbar/Navbar";
import WebLayout from "@/Layout/WebLayout";
import type { Metadata } from "next";
export const metadata: Metadata = {
  title: "Minimal Vendor Website",
  description: "Create By <PERSON><PERSON><PERSON><PERSON> hasan <PERSON>",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <LenisScroll>
        <Navbar />
        <main>
          <WebLayout>{children}</WebLayout>
        </main>
        <Footer />
      </LenisScroll>
    </>
  );
}
