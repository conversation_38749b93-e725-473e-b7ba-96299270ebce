import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import React, { useState } from "react";
interface CartSummaryProps {
  subtotal: number;
}
export const CartSummary: React.FC<CartSummaryProps> = ({ subtotal }) => {
  const [isCouponOpen, setIsCouponOpen] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [discount, setDiscount] = useState(0);
  const [error, setError] = useState("");
  const [amount, setAmount] = useState(subtotal.toLocaleString());
  const [isCouponApplied, setIsCouponApplied] = useState(false);

  const handleApplyCoupon = () => {
    if (isCouponApplied) {
      alert("Please, you cannot use it twice.");
      return;
    }
    if (couponCode.toUpperCase() === "WELCOME10") {
      const couponDiscount = subtotal * 0.1; // 10% discount
      setDiscount(couponDiscount);
      setError("");
      setIsCouponOpen(false);
      setIsCouponApplied(true);
      const finalSubtotal = subtotal - discount - 2500;
      setAmount(finalSubtotal.toLocaleString());
    } else {
      setError("Invalid coupon code. Please try again.");
      setDiscount(0);
    }
  };

  return (
    <>
      <div className="p-4 border-t border-gray-500 w-full  flex  flex-col justify-center">
        <div className="flex justify-between items-center mb-4">
          <p className="text-lg font-semibold">Subtotal</p>
          <p className="text-lg font-semibold">₹{amount}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500 mb-2">
            You will save ₹2,500 on this order (Estimated savings)
          </p>
          {discount > 0 && (
            <p className="text-sm text-green-600 mb-2">
              Coupon Discount: ₹{discount.toLocaleString()} (WELCOME10 - 10%
              off)
            </p>
          )}
          <Button
            variant="outline"
            onClick={() => setIsCouponOpen(true)}
            className="w-full mb-4"
          >
            Have a Coupon Code?
          </Button>
        </div>
        <div className="flex flex-col gap-2">
          <Link href={"/checkout"}>
            <Button className="w-full bg-black hover:bg-gray-950 active:scale-95 transition-all ease-linear duration-200 text-white h-14">
              Proceed to Checkout
            </Button>
          </Link>
          <p className="text-sm text-gray-500 text-center py-4">
            No Cost EMI / Cards / NetBanking / COD
          </p>
          <Link href={"/"}>
            <Button variant="outline" className="w-full h-14">
              Continue Shopping
            </Button>
          </Link>
        </div>
      </div>
      {/* Coupon Dialog/Modal */}
      <Dialog open={isCouponOpen} onOpenChange={setIsCouponOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Apply Coupon Code</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="coupon">Coupon Code</Label>
              <Input
                id="coupon"
                value={couponCode}
                onChange={(e) => setCouponCode(e.target.value)}
                placeholder="Enter coupon code"
              />
              {error && <p className="text-red-500 text-sm">{error}</p>}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCouponOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApplyCoupon}>Apply</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
