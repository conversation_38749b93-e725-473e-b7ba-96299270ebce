import Image from "next/image";
import { Marquee } from "../magicui/marquee";

const reviews = [
  {
    id: 1,
    img: "https://www.justwatches.com/cdn/shop/files/timex_0ef00bfd-9cbe-4382-9c24-44746b70f47b.jpg?v=1712744952&width=180",
  },
  {
    id: 2,
    img: "https://www.justwatches.com/cdn/shop/files/guess_24bde36a-99e4-4d62-a480-c111542560d5.jpg?v=1712745026&width=180",
  },
  {
    id: 3,
    img: "https://www.justwatches.com/cdn/shop/files/gc_39208913-739d-411a-9334-38758f4d29e3.jpg?v=1712745105&width=180",
  },
  {
    id: 4,
    img: "https://www.justwatches.com/cdn/shop/files/versace_99b2d184-d273-48a9-9d3d-42cf10e924b1.jpg?v=1712745168&width=180",
  },
  {
    id: 5,
    img: "https://www.justwatches.com/cdn/shop/files/nautica_7b5a06f8-f4a4-4e9a-bb04-54f25655d96a.jpg?v=1712747103&width=180",
  },
  {
    id: 6,
    img: "https://www.justwatches.com/cdn/shop/files/ucb_b2b6f6bd-d4f9-4150-9ade-dca0907b3c96.jpg?v=1712747538&width=180",
  },
  {
    id: 7,
    img: "https://www.justwatches.com/cdn/shop/files/PP_6192c9f5-2fce-4ffb-b291-6e7a60374bbf.jpg?v=1712747813&width=180",
  },
  {
    id: 8,
    img: "https://www.justwatches.com/cdn/shop/files/PS_de13b970-fce6-4b39-8f71-3a3089929b2e.jpg?v=1712747867&width=180",
  },
  {
    id: 9,
    img: "https://www.justwatches.com/cdn/shop/files/tb.jpg?v=1712747959&width=180",
  },
];

const ReviewCard = ({ img }: { img: string }) => {
  return (
    <div className="size-[180px]">
      <Image
        className="h-full w-full object-cover"
        width={1280}
        height={8000}
        layout="responsive"
        priority={true}
        quality={100}
        alt="Brand Logo"
        src={img}
      />
    </div>
  );
};

export default function BrandShow() {
  return (
    <div className="relative flex w-full flex-col items-center justify-center overflow-hidden">
      <Marquee pauseOnHover className="[--duration:100s]">
        {reviews.map((review) => (
          <div key={review.id} className="flex items-center gap-8">
            <ReviewCard {...review} />
          </div>
        ))}
      </Marquee>
    </div>
  );
}
