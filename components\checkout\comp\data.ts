export const countries = [
  "United States",
  "Canada",
  "India",
  "Australia",
  "United Kingdom",
];
export const regions = [
  "California",
  "Ontario",
  "Maharashtra",
  "New South Wales",
  "England",
];
export const cities = ["Los Angeles", "Toronto", "Mumbai", "Sydney", "London"];

export const getCountry = (isoCode: string) => {
  const country = countries.find(
    (country) => country.toLowerCase() === isoCode.toLowerCase()
  );
  return country;
};

export const getRegion = (isoCode: string) => {
  const region = regions.find(
    (region) => region.toLowerCase() === isoCode.toLowerCase()
  );
  return region;
};
