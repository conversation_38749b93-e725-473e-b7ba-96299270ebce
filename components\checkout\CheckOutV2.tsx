/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import axios from "axios";
import { useState } from "react";

// data
import { clearCart } from "@/Store/Features/addToCardSlice";
import { useAppDispatch, useAppSelector } from "@/Store/hooks/reduxHook";
import Image from "next/image";
import ThankYouPage from "../ThankYou/ThankYou";

interface CustomerInfo {
  name: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

// global styles
const globalStyles = {
  inputStyles:
    "border border-gray-200 w-full py-2 px-4 rounded-md mt-1 outline-none focus:border-[#0FABCA]",
  labelStyles: "text-[14px] font-[400] text-gray-700",
};

// select component

const CheckoutComponets = () => {
  const dispatch = useAppDispatch();
  const { items: cartItems, totalAmount } = useAppSelector(
    (state) => state.cart
  );

  const [selectedPayment, setSelectedPayment] = useState("cash_on_delivery");
  const [isChecked, setIsChecked] = useState(false);
  const [showThanks, setShowThanks] = useState(false);
  const [loading, setLoading] = useState(false);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: "",
    email: "",
    phone: "",
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: "Bangladesh",
    },
  });
  const [notes, setNotes] = useState("");

  // Calculate totals from Redux cart
  const subtotal = totalAmount;
  const shipping = cartItems.length > 0 ? 100 : 0; // Free shipping if no items
  const tax = 0; // No tax for now
  const discount = 0; // No discount for now
  const total = subtotal + shipping + tax - discount;

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setIsChecked(true);
    } else {
      setIsChecked(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith("address.")) {
      const addressField = field.split(".")[1];
      setCustomerInfo((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value,
        },
      }));
    } else {
      setCustomerInfo((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const validateForm = () => {
    const required = [
      customerInfo.name,
      customerInfo.email,
      customerInfo.phone,
      customerInfo.address.street,
      customerInfo.address.city,
      customerInfo.address.state,
      customerInfo.address.zipCode,
      customerInfo.address.country,
    ];

    return required.every((field) => field.trim() !== "");
  };

  const handleSubmit = async () => {
    if (cartItems.length === 0) {
      alert("Your cart is empty. Please add some products before checkout.");
      return;
    }

    if (!validateForm()) {
      alert("Please fill in all required fields");
      return;
    }

    setLoading(true);

    try {
      // Transform cart items to match API format
      const transformedItems = cartItems.map((item) => ({
        productId: item._id,
        productName: item.name,
        productImage: item.imgUrl[0]?.url || "",
        price: item.price,
        salePrice: item.salePrice,
        quantity: item.quantity,
        total: item.salePrice * item.quantity,
      }));

      const orderData = {
        customerInfo,
        items: transformedItems,
        tax,
        shipping,
        discount,
        paymentMethod: selectedPayment,
        notes,
      };

      const response = await axios.post("/api/order", orderData);

      if (response.data.success) {
        console.log("Order created:", response.data.data);
        // Clear cart after successful order
        dispatch(clearCart());
        setShowThanks(true);
      } else {
        alert(`Error: ${response.data.message}`);
      }
    } catch (error: any) {
      console.error("Error creating order:", error);

      if (error.response?.data?.message) {
        alert(`Error: ${error.response.data.message}`);
      } else if (error.response?.data?.errors) {
        alert(`Validation Error: ${error.response.data.errors.join(", ")}`);
      } else {
        alert("Failed to place order. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };
  return (
    <>
      {!showThanks ? (
        <>
          <div className="max-w-7xl mx-auto py-20">
            <div className="grid gap-8 grid-cols-1 lg:grid-cols-3 w-full">
              {/* Billing and Payment Form */}
              <div className="md:col-span-2 space-y-8 w-full">
                {/* Billing Information */}
                <div className="w-full bg-white border rounded-lg px-4 py-6">
                  <h2 className="text-[1.5rem] font-medium text-gray-700 mb-6">
                    Billing Information
                  </h2>

                  <div className="grid grid-cols-1 gap-4 w-full max-w-4xl mx-auto">
                    {/* Name Fields */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="w-full">
                        <label
                          htmlFor="firstName"
                          className={`${globalStyles.labelStyles}`}
                        >
                          First name
                        </label>
                        <input
                          placeholder="Full name"
                          type="text"
                          id="firstName"
                          value={customerInfo.name}
                          onChange={(e) =>
                            handleInputChange("name", e.target.value)
                          }
                          className={`${globalStyles.inputStyles}`}
                          required
                        />
                      </div>
                      <div className="w-full">
                        <label
                          htmlFor="phone"
                          className={`${globalStyles.labelStyles}`}
                        >
                          Phone Number *
                        </label>
                        <input
                          placeholder="Phone number"
                          type="tel"
                          id="phone"
                          value={customerInfo.phone}
                          onChange={(e) =>
                            handleInputChange("phone", e.target.value)
                          }
                          className={`${globalStyles.inputStyles}`}
                          required
                        />
                      </div>
                    </div>

                    {/* Email */}
                    <div>
                      <label
                        htmlFor="email"
                        className={`${globalStyles.labelStyles}`}
                      >
                        Email Address *
                      </label>
                      <input
                        placeholder="Email address"
                        type="email"
                        id="email"
                        value={customerInfo.email}
                        onChange={(e) =>
                          handleInputChange("email", e.target.value)
                        }
                        className={`${globalStyles.inputStyles}`}
                        required
                      />
                    </div>

                    {/* Address */}
                    <div>
                      <label
                        htmlFor="address"
                        className={`${globalStyles.labelStyles}`}
                      >
                        Street Address *
                      </label>
                      <input
                        placeholder="Street address"
                        type="text"
                        id="address"
                        value={customerInfo.address.street}
                        onChange={(e) =>
                          handleInputChange("address.street", e.target.value)
                        }
                        className={`${globalStyles.inputStyles}`}
                        required
                      />
                    </div>

                    {/* Country and State */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="w-full">
                        <label
                          htmlFor="country"
                          className={`${globalStyles.labelStyles}`}
                        >
                          Country *
                        </label>
                        <input
                          placeholder="Country"
                          type="text"
                          id="country"
                          value={customerInfo.address.country}
                          onChange={(e) =>
                            handleInputChange("address.country", e.target.value)
                          }
                          className={`${globalStyles.inputStyles}`}
                          required
                        />
                      </div>
                      <div className="w-full">
                        <label
                          htmlFor="state"
                          className={`${globalStyles.labelStyles}`}
                        >
                          Region/State *
                        </label>
                        <input
                          placeholder="State/Region"
                          type="text"
                          id="state"
                          value={customerInfo.address.state}
                          onChange={(e) =>
                            handleInputChange("address.state", e.target.value)
                          }
                          className={`${globalStyles.inputStyles}`}
                          required
                        />
                      </div>
                    </div>

                    {/* City and Zip */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="w-full">
                        <label
                          htmlFor="city"
                          className={`${globalStyles.labelStyles}`}
                        >
                          City *
                        </label>
                        <input
                          placeholder="City"
                          type="text"
                          id="city"
                          value={customerInfo.address.city}
                          onChange={(e) =>
                            handleInputChange("address.city", e.target.value)
                          }
                          className={`${globalStyles.inputStyles}`}
                          required
                        />
                      </div>
                      <div className="w-full">
                        <label
                          htmlFor="zipCode"
                          className={`${globalStyles.labelStyles}`}
                        >
                          Zip Code *
                        </label>
                        <input
                          placeholder="Zip code"
                          type="text"
                          id="zipCode"
                          value={customerInfo.address.zipCode}
                          onChange={(e) =>
                            handleInputChange("address.zipCode", e.target.value)
                          }
                          className={`${globalStyles.inputStyles}`}
                          required
                        />
                      </div>
                    </div>

                    {/* Checkbox */}
                    <div className="mt-4">
                      <label className="flex items-center gap-[10px] cursor-pointer">
                        <input
                          type="checkbox"
                          className="hidden"
                          onChange={handleCheckboxChange}
                        />
                        {isChecked ? (
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g id="Group 335">
                              <rect
                                id="Rectangle 331"
                                x="-0.00012207"
                                y="6.10352e-05"
                                width="20"
                                height="20"
                                rx="4"
                                className="fill-[#0FABCA]"
                                stroke="#0FABCA"
                              ></rect>
                              <path
                                id="Vector"
                                d="M8.19594 15.4948C8.0646 15.4949 7.93453 15.4681 7.81319 15.4157C7.69186 15.3633 7.58167 15.2865 7.48894 15.1896L4.28874 11.8566C4.10298 11.6609 3.99914 11.3965 3.99988 11.1213C4.00063 10.8461 4.10591 10.5824 4.29272 10.3878C4.47953 10.1932 4.73269 10.0835 4.99689 10.0827C5.26109 10.0819 5.51485 10.1901 5.70274 10.3836L8.19591 12.9801L14.2887 6.6335C14.4767 6.4402 14.7304 6.3322 14.9945 6.33307C15.2586 6.33395 15.5116 6.44362 15.6983 6.63815C15.8851 6.83268 15.9903 7.09627 15.9912 7.37137C15.992 7.64647 15.8883 7.91073 15.7027 8.10648L8.90294 15.1896C8.8102 15.2865 8.7 15.3633 8.57867 15.4157C8.45734 15.4681 8.32727 15.4949 8.19594 15.4948Z"
                                fill="white"
                              ></path>
                            </g>
                          </svg>
                        ) : (
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g id="Group 335">
                              <rect
                                id="Rectangle 331"
                                x="-0.00012207"
                                y="6.10352e-05"
                                width="20"
                                height="20"
                                rx="4"
                                className="fill-transparent"
                                stroke="#ccc"
                              ></rect>
                            </g>
                          </svg>
                        )}
                        <span className="text-[0.9rem] text-gray-700">
                          Ship to a different address
                        </span>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Payment Options */}
                <div className="border border-gray-200 rounded-md">
                  <h2 className="text-[1.2rem] font-medium text-gray-700 border-b border-gray-200 px-5 py-3">
                    Payment Option
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full p-5">
                    <button
                      onClick={() => setSelectedPayment("cash_on_delivery")}
                      className={`flex flex-col items-center justify-center p-4 border rounded-lg ${
                        selectedPayment === "cash_on_delivery"
                          ? "border-[#0FABCA]"
                          : "border-gray-200"
                      }`}
                    >
                      <span className="text-2xl">💵</span>
                      <span className="text-[0.9rem] text-gray-700 font-[500] mt-2">
                        Cash on Delivery
                      </span>
                    </button>
                    <button
                      onClick={() => setSelectedPayment("bkash")}
                      className={`flex flex-col items-center justify-center p-4 border rounded-lg ${
                        selectedPayment === "bkash"
                          ? "border-[#0FABCA]"
                          : "border-gray-200"
                      }`}
                    >
                      <span className="text-2xl">�</span>
                      <span className="text-[0.9rem] text-gray-700 font-[500] mt-2">
                        bKash
                      </span>
                    </button>
                  </div>

                  {selectedPayment === "credit-card" && (
                    <div className=" px-5 pb-5 space-y-[16px]">
                      <div>
                        <label
                          htmlFor="cardName"
                          className={`${globalStyles.labelStyles}`}
                        >
                          Name on Card
                        </label>
                        <input
                          placeholder="Name on card"
                          type="text"
                          id="cardName"
                          className={`${globalStyles.inputStyles}`}
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="cardNumber"
                          className={`${globalStyles.labelStyles}`}
                        >
                          Card Number
                        </label>
                        <input
                          placeholder="Card number"
                          type="text"
                          id="cardNumber"
                          className={`${globalStyles.inputStyles}`}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label
                            htmlFor="expireDate"
                            className={`${globalStyles.labelStyles}`}
                          >
                            Expire Date
                          </label>
                          <input
                            type="text"
                            id="expireDate"
                            placeholder="MM/YY"
                            className={`${globalStyles.inputStyles}`}
                          />
                        </div>
                        <div>
                          <label
                            htmlFor="cvc"
                            className={`${globalStyles.labelStyles}`}
                          >
                            CVC
                          </label>
                          <input
                            placeholder="CVC"
                            type="text"
                            id="cvc"
                            className={`${globalStyles.inputStyles}`}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Additional Information */}
                <div>
                  <h2 className="text-[1.2rem] font-medium text-gray-700 mb-4">
                    Additional Information
                  </h2>
                  <div>
                    <label
                      htmlFor="notes"
                      className={`${globalStyles.labelStyles}`}
                    >
                      Order Notes (Optional)
                    </label>
                    <textarea
                      id="notes"
                      rows={4}
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Notes about your order e.g. special notes for delivery"
                      className={`${globalStyles.inputStyles} py-3`}
                    />
                  </div>
                </div>
              </div>

              {/* Order Summary */}
              <div className="w-full">
                <div className="bg-white rounded-md border border-gray-200 p-6">
                  <h2 className="text-[1.2rem] font-medium text-gray-700 mb-6">
                    Order Summary
                  </h2>
                  <div className="space-y-4">
                    {cartItems.length > 0 ? (
                      cartItems.map((item) => (
                        <div
                          key={item._id}
                          className="flex items-center space-x-4"
                        >
                          <div className="flex-shrink-0">
                            <Image
                              width={50}
                              height={50}
                              src={
                                item.imgUrl[0]?.url || "/placeholder-image.jpg"
                              }
                              alt={item.name}
                              className="w-[50px] h-[50px] object-cover rounded"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 line-clamp-2">
                              {item.name}
                            </p>
                            <div className="flex items-center gap-[5px] mt-0.5">
                              <p className="text-sm text-gray-500">
                                {item.quantity} x{" "}
                              </p>
                              <p className="text-sm text-[#0FABCA] font-[600]">
                                ৳{item.salePrice.toFixed(2)}
                              </p>
                            </div>
                            {item.brand && (
                              <p className="text-xs text-gray-400 mt-1">
                                Brand: {item.brand}
                              </p>
                            )}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-gray-500">Your cart is empty</p>
                        <p className="text-sm text-gray-400 mt-1">
                          Add some products to continue
                        </p>
                      </div>
                    )}

                    <div className="pt-4 space-y-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Sub-total</span>
                        <span className="font-medium text-gray-800">
                          ৳{subtotal.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Shipping</span>
                        <span className="font-medium text-gray-800">
                          ৳{shipping.toFixed(2)}
                        </span>
                      </div>
                      {discount > 0 && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Discount</span>
                          <span className="font-medium text-green-600">
                            -৳{discount.toFixed(2)}
                          </span>
                        </div>
                      )}
                      {tax > 0 && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Tax</span>
                          <span className="font-medium text-gray-800">
                            ৳{tax.toFixed(2)}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between">
                        <span className="text-base font-medium text-gray-800">
                          Total
                        </span>
                        <span className="text-base font-medium text-gray-800">
                          ৳{(subtotal + shipping + tax - discount).toFixed(2)}
                        </span>
                      </div>
                    </div>

                    <button
                      onClick={handleSubmit}
                      disabled={loading}
                      className="w-full bg-[#060909] text-white py-3 px-4 rounded-lg hover:bg-[#0FABCA]/90 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      {loading ? "PLACING ORDER..." : "PLACE ORDER"}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          {" "}
          <div>
            <ThankYouPage />
          </div>
        </>
      )}
    </>
  );
};

export default CheckoutComponets;
