/* eslint-disable @next/next/no-img-element */
import { But<PERSON> } from "@/components/ui/button";
import { MinusIcon, PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import React from "react";

interface CartItemProps {
  name: string;
  price: number;
  quantity: number;
  image: string;
  onQuantityChange: (newQuantity: number) => void;
  onRemove: () => void;
}

export const CartItem: React.FC<CartItemProps> = ({
  name,
  price,
  quantity,
  image,
  onQuantityChange,
  onRemove,
}) => {
  const total = price * quantity;

  return (
    <div className="flex items-center flex-col md:flex-row p-6 md:p-0 justify-between  border-b border-slate-800">
      <div className="flex items-center  gap-4 py-5">
        <img
          src={image}
          alt={name}
          className="w-32  h-32 object-contain rounded"
        />
        <div>
          <h3 className="font-semibold text-sm md:text-base">{name}</h3>
          <p className="text-xs text-gray-500">42 x 50 mm</p>
        </div>
      </div>
      <div className="flex items-center gap-4">
        <p className="text-sm md:text-base">₹{price.toLocaleString()}</p>
        <div className="flex items-center gap-2 border border-gray-300 rounded-md p-1">
          <Button
            variant="outline"
            size="icon"
            onClick={() => onQuantityChange(quantity - 1)}
            disabled={quantity === 1}
            className="h-8 w-8"
          >
            <MinusIcon className="h-4 w-4" />
          </Button>
          <span className="w-8 text-center">{quantity}</span>
          <Button
            variant="outline"
            size="icon"
            onClick={() => onQuantityChange(quantity + 1)}
            className="h-8 w-8"
          >
            <PlusIcon className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-sm md:text-base">₹{total.toLocaleString()}</p>
        <Button
          variant="outline"
          size="icon"
          onClick={onRemove}
          className="h-8 w-8"
        >
          <TrashIcon className="h-4 w-4 text-red-500" />
        </Button>
      </div>
    </div>
  );
};
