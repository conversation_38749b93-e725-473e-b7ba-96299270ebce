import Link from "next/link";

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
        <h2 className="text-2xl font-semibold text-gray-700 mb-4">
          Product Not Found
        </h2>
        <p className="text-gray-600 mb-8 max-w-md">
          {`  Sorry, the product you&apos;re looking for doesn&apos;t exist or has
          been removed.`}
        </p>
        <div className="space-y-4">
          <Link
            href="/"
            className="inline-block bg-[#0FABCA] text-white px-6 py-3 rounded-md hover:bg-[#0FABCA]/90 transition-colors"
          >
            Go Back Home
          </Link>
          <div>
            <Link href="/products" className="text-[#0FABCA] hover:underline">
              Browse All Products
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
