/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import ProductCard from "@/components/AdminPannel/Product/ProductCard";
import axios from "axios";
import Link from "next/link";
import { useEffect, useState } from "react";

// Define the product interface based on our API
interface Product {
  _id: string;
  name: string;
  shortDescription: string;
  description: string;
  price: number;
  salePrice: number;
  imgUrl: Array<{
    id: string;
    imageName: string;
    url: string;
  }>;
  descriptionImages: {
    image1: string;
    image2: string;
  };
  isSale: boolean;
  isHaveCoupon: boolean;
  couponCode?: string;
  createdAt: string;
  updatedAt: string;
}

const Products = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch products from API
  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await axios.get("/api/product");

      if (response.data.success) {
        setProducts(response.data.data);
      } else {
        setError("Failed to fetch products");
      }
    } catch (error: any) {
      console.error("Error fetching products:", error);
      setError("Failed to fetch products");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  if (loading) {
    return (
      <section>
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-semibold">Products</h2>
          <Link href={"/admin/products/new"}>
            <button className="bg-black text-white px-4 py-2 rounded-md">
              Add New Product
            </button>
          </Link>
        </div>
        <div className="mt-6 flex justify-center">
          <div className="text-lg">Loading products...</div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section>
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-semibold">Products</h2>
          <Link href={"/admin/products/new"}>
            <button className="bg-black text-white px-4 py-2 rounded-md">
              Add New Product
            </button>
          </Link>
        </div>
        <div className="mt-6 flex justify-center">
          <div className="text-red-500 text-lg">{error}</div>
        </div>
      </section>
    );
  }

  return (
    <section>
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Products ({products.length})</h2>
        <Link href={"/admin/products/new"}>
          <button className="bg-black text-white px-4 py-2 rounded-md">
            Add New Product
          </button>
        </Link>
      </div>

      {products.length === 0 ? (
        <div className="mt-6 text-center py-12">
          <div className="text-gray-500 text-lg">No products found</div>
          <p className="text-gray-400 mt-2">
            Create your first product to get started
          </p>
        </div>
      ) : (
        <div className="mt-6 grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 xl:gap-x-8">
          {products.map((product) => (
            <ProductCard key={product._id} product={product} />
          ))}
        </div>
      )}
    </section>
  );
};

export default Products;
