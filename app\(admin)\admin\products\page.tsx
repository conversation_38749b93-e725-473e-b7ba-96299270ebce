import ProductCard from "@/components/AdminPannel/Product/ProductCard";
import Link from "next/link";
export type Product = {
  id: number;
  name: string;
  href: string;
  color: string;
  price: string;
  imageSrc: string;
  imageAlt: string;
};
const products: Product[] = [
  {
    id: 1,
    name: "Basic Tee",
    href: "#",
    imageSrc:
      "https://tailwindcss.com/plus-assets/img/ecommerce-images/product-page-01-related-product-01.jpg",
    imageAlt: "Front of men's Basic Tee in black.",
    price: "$35",
    color: "Black",
  },
];
const Products = () => {
  return (
    <section>
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Products</h2>
        <Link href={"/admin/products/new"}>
          <button className="bg-black text-white px-4 py-2 rounded-md">
            Add New Product
          </button>
        </Link>
      </div>
      <div className="mt-6 grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-2 lg:grid-cols-4 xl:gap-x-8">
        <ProductCard product={products[0]} />
      </div>
    </section>
  );
};

export default Products;
