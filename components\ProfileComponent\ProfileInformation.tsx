"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { useState } from "react";

export default function ProfileInformation({
  imgurl,
  name,
  email,
}: {
  imgurl: string;
  name: string;
  email: string;
}) {
  // State to store form data with new fields
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    company: "",
    street: "",
    aptSuite: "",
    city: "",
    state: "",
    zip: "",
    country: "India", // Default value from image
    phone: "",
  });

  // State to track if form has been submitted
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleFormSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const form = event.target as HTMLFormElement;
    const newData = {
      firstName: form.firstName.value,
      lastName: form.lastName.value,
      company: form.company.value,
      street: form.street.value,
      aptSuite: form.aptSuite.value,
      city: form.city.value,
      state: form.state.value,
      zip: form.zip.value,
      country: form.country.value,
      phone: form.phone.value,
    };
    setFormData(newData);
    setIsSubmitted(true);
    console.log("Form submitted with data:", newData);
  };

  // Handler for input changes to update state in real-time
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center mb-6">
          <Image
            src={imgurl}
            alt="User Avatar"
            width={100}
            height={100}
            className="rounded-full size-28 object-cover"
          />
          <h3 className="text-xl font-bold mt-4">{name}</h3>
          <p className="text-muted-foreground">{email}</p>
        </div>

        {/* Display submitted data if form has been submitted */}
        {isSubmitted ? (
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>First Name</Label>
                <p className="text-muted-foreground">{formData.firstName}</p>
              </div>
              <div className="space-y-2">
                <Label>Last Name</Label>
                <p className="text-muted-foreground">{formData.lastName}</p>
              </div>
              <div className="space-y-2">
                <Label>Company</Label>
                <p className="text-muted-foreground">{formData.company}</p>
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Street Address</Label>
                <p className="text-muted-foreground">{formData.street}</p>
              </div>
              <div className="space-y-2">
                <Label>Apt, Suite, etc</Label>
                <p className="text-muted-foreground">{formData.aptSuite}</p>
              </div>
              <div className="space-y-2">
                <Label>City</Label>
                <p className="text-muted-foreground">{formData.city}</p>
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Country</Label>
                <p className="text-muted-foreground">{formData.country}</p>
              </div>
              <div className="space-y-2">
                <Label>State/Province</Label>
                <p className="text-muted-foreground">{formData.state}</p>
              </div>
              <div className="space-y-2">
                <Label>Zip Code</Label>
                <p className="text-muted-foreground">{formData.zip}</p>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Phone</Label>
              <p className="text-muted-foreground">{formData.phone}</p>
            </div>
            <div className="space-y-2">
              <Label>Set as default address</Label>
              <p className="text-muted-foreground">
                {formData.street ||
                formData.city ||
                formData.state ||
                formData.zip
                  ? "Yes"
                  : "No"}
              </p>
            </div>
            <Button onClick={() => setIsSubmitted(false)} className="w-full">
              Edit Information
            </Button>
          </div>
        ) : (
          // Show form if not submitted or in edit mode
          <form className="space-y-10" onSubmit={handleFormSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  type="text"
                  required
                  className="w-full py-5 px-3 rounded-md border border-gray-300"
                  onChange={handleInputChange}
                  placeholder="John"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  type="text"
                  required
                  className="w-full py-5 px-3 rounded-md border border-gray-300"
                  onChange={handleInputChange}
                  placeholder="Doe"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="company">Company</Label>
                <Input
                  id="company"
                  name="company"
                  value={formData.company}
                  type="text"
                  className="w-full py-5 px-3 rounded-md border border-gray-300"
                  onChange={handleInputChange}
                  placeholder="Acme Corp"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="street">Street Address</Label>
                <Input
                  id="street"
                  name="street"
                  value={formData.street}
                  type="text"
                  required
                  className="w-full py-5 px-3 rounded-md border border-gray-300"
                  onChange={handleInputChange}
                  placeholder="123 Main St"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="aptSuite">Apt, Suite, etc</Label>
                <Input
                  id="aptSuite"
                  name="aptSuite"
                  value={formData.aptSuite}
                  type="text"
                  className="w-full py-5 px-3 rounded-md border border-gray-300"
                  onChange={handleInputChange}
                  placeholder="Apt 4B"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  name="city"
                  value={formData.city}
                  type="text"
                  required
                  className="w-full py-5 px-3 rounded-md border border-gray-300"
                  onChange={handleInputChange}
                  placeholder="Anytown"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  name="country"
                  value={formData.country}
                  type="text"
                  required
                  className="w-full py-5 px-3 rounded-md border border-gray-300"
                  onChange={handleInputChange}
                  placeholder="India"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">State/Province</Label>
                <Input
                  id="state"
                  name="state"
                  value={formData.state}
                  type="text"
                  required
                  className="w-full py-5 px-3 rounded-md border border-gray-300"
                  onChange={handleInputChange}
                  placeholder="CA"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="zip">Zip Code</Label>
                <Input
                  id="zip"
                  name="zip"
                  value={formData.zip}
                  type="text"
                  required
                  className="w-full py-8 px-3 rounded-md border border-gray-300"
                  onChange={handleInputChange}
                  placeholder="12345"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone}
                type="tel"
                required
                className="w-fullpy-5 px-3 rounded-md border border-gray-300"
                onChange={handleInputChange}
                placeholder="******-456-7890"
              />
            </div>
            <div className="space-y-2 flex  items-center gap-4 w-full">
              <Label>Set as default address</Label>
              <input
                type="checkbox"
                name="isDefault"
                className="w-4 h-4"
                onChange={(e) =>
                  console.log("Default address checkbox:", e.target.checked)
                }
              />
            </div>
            <Button type="submit" className="w-full">
              Save Changes
            </Button>
          </form>
        )}
      </CardContent>
    </Card>
  );
}
