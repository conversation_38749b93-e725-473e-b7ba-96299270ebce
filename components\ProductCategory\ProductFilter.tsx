"use client";

import type React from "react";

import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";
const categories = ["T-Shirts", "Dresses", "Pants", "Jackets", "Sweaters"];

interface FilterSectionProps {
  title: string;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

const ProductFilter = () => {
  const [openSections, setOpenSections] = useState<string[]>(["Category"]);

  const toggleSection = (section: string) => {
    setOpenSections((current) =>
      current.includes(section)
        ? current.filter((s) => s !== section)
        : [...current, section]
    );
  };
  const isOpen = (section: string) => openSections.includes(section);
  return (
    <div className="w-full md:w-64 bg-white">
      <div className="flex items-center justify-between pb-4 border-b">
        <h2 className="font-medium">Filters</h2>
        <button className="text-sm text-gray-500 hover:text-gray-900">
          Reset all
        </button>
      </div>

      <FilterSection
        title="Category"
        isOpen={isOpen("Category")}
        onToggle={() => toggleSection("Category")}
      >
        <div className="space-y-2">
          {categories.map((category) => (
            <div key={category} className="flex items-center space-x-2">
              <Checkbox id={category} />
              <Label htmlFor={category} className="text-sm font-normal">
                {category}
              </Label>
            </div>
          ))}
        </div>
      </FilterSection>

      <FilterSection
        title="Price"
        isOpen={isOpen("Price")}
        onToggle={() => toggleSection("Price")}
      >
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox id="price-1" />
            <Label htmlFor="price-1" className="text-sm font-normal">
              $0 - $50
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="price-2" />
            <Label htmlFor="price-2" className="text-sm font-normal">
              $51 - $100
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="price-3" />
            <Label htmlFor="price-3" className="text-sm font-normal">
              $101 - $200
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="price-4" />
            <Label htmlFor="price-4" className="text-sm font-normal">
              $201+
            </Label>
          </div>
        </div>
      </FilterSection>

      <FilterSection
        title="Size"
        isOpen={isOpen("Size")}
        onToggle={() => toggleSection("Size")}
      >
        <div className="space-y-2">
          {["XS", "S", "M", "L", "XL", "XXL"].map((size) => (
            <div key={size} className="flex items-center space-x-2">
              <Checkbox id={`size-${size}`} />
              <Label htmlFor={`size-${size}`} className="text-sm font-normal">
                {size}
              </Label>
            </div>
          ))}
        </div>
      </FilterSection>

      <FilterSection
        title="Color"
        isOpen={isOpen("Color")}
        onToggle={() => toggleSection("Color")}
      >
        <div className="space-y-2">
          {["Black", "White", "Gray", "Navy", "Red", "Blue"].map((color) => (
            <div key={color} className="flex items-center space-x-2">
              <Checkbox id={`color-${color}`} />
              <Label htmlFor={`color-${color}`} className="text-sm font-normal">
                {color}
              </Label>
            </div>
          ))}
        </div>
      </FilterSection>

      <FilterSection
        title="Pattern"
        isOpen={isOpen("Pattern")}
        onToggle={() => toggleSection("Pattern")}
      >
        <div className="space-y-2">
          {["Solid", "Striped", "Floral", "Checked"].map((pattern) => (
            <div key={pattern} className="flex items-center space-x-2">
              <Checkbox id={`pattern-${pattern}`} />
              <Label
                htmlFor={`pattern-${pattern}`}
                className="text-sm font-normal"
              >
                {pattern}
              </Label>
            </div>
          ))}
        </div>
      </FilterSection>

      <FilterSection
        title="Material"
        isOpen={isOpen("Material")}
        onToggle={() => toggleSection("Material")}
      >
        <div className="space-y-2">
          {["Cotton", "Polyester", "Linen", "Wool", "Denim"].map((material) => (
            <div key={material} className="flex items-center space-x-2">
              <Checkbox id={`material-${material}`} />
              <Label
                htmlFor={`material-${material}`}
                className="text-sm font-normal"
              >
                {material}
              </Label>
            </div>
          ))}
        </div>
      </FilterSection>

      <FilterSection
        title="Brand"
        isOpen={isOpen("Brand")}
        onToggle={() => toggleSection("Brand")}
      >
        <div className="space-y-2">
          {["Nike", "Adidas", "H&M", "Zara", "Uniqlo"].map((brand) => (
            <div key={brand} className="flex items-center space-x-2">
              <Checkbox id={`brand-${brand}`} />
              <Label htmlFor={`brand-${brand}`} className="text-sm font-normal">
                {brand}
              </Label>
            </div>
          ))}
        </div>
      </FilterSection>
    </div>
  );
};
function FilterSection({
  title,
  isOpen,
  onToggle,
  children,
}: FilterSectionProps) {
  return (
    <div className="border-b">
      <button
        onClick={onToggle}
        className="flex w-full items-center justify-between py-3 text-sm"
      >
        {title}
        {isOpen ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </button>
      <div
        className={cn(
          "overflow-hidden transition-all duration-200",
          isOpen ? "max-h-96 pb-3" : "max-h-0"
        )}
      >
        {children}
      </div>
    </div>
  );
}
export default ProductFilter;
