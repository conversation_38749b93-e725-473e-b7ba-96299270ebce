/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { Editor } from "primereact/editor";
import { useState } from "react";
const NewProduct = () => {
  const [text, setText] = useState("");
  const [product, setProduct] = useState({
    name: "",
    shortDescription: "",
    price: "",
    salePrice: "",
    imgUrl: [
      {
        id: "",
        imageName: "",
        url: "",
      },
    ],
  });

  const handleChange = (field: string, value: any) => {
    setProduct((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Product Data:", product, text);
  };

  return (
    <div className="">
      <div className="flex justify-between items-center mb-5">
        <h1 className="text-2xl font-semibold mb-4">Add New Product</h1>
        <Link href={"/admin/products"}>
          <button className="bg-black text-white px-4 py-2 rounded-md">
            Back to Products
          </button>
        </Link>
      </div>
      <Card className="py-5">
        <CardContent>
          <form
            onSubmit={handleSubmit}
            className="grid grid-cols-1 lg:grid-cols-2 gap-4"
          >
            <div>
              <div className="space-y-4">
                <div>
                  <label className="block font-medium mb-3">Product Name</label>
                  <input
                    type="text"
                    placeholder="Product Name"
                    value={product.name}
                    onChange={(e) => handleChange("name", e.target.value)}
                    className="w-full border p-2 rounded py-4"
                  />
                </div>
                <div>
                  <label className="block font-medium mb-3">
                    Short Product Description
                  </label>
                  <textarea
                    placeholder="Product Description"
                    value={product.shortDescription}
                    onChange={(e) =>
                      handleChange("shortDescription", e.target.value)
                    }
                    className="w-full border p-2 rounded min-h-16"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <input
                    type="number"
                    placeholder="Price"
                    value={product.price}
                    onChange={(e) => handleChange("price", e.target.value)}
                    className="w-full border p-2 rounded"
                  />
                  <input
                    type="number"
                    placeholder="Sale Price"
                    value={product.salePrice}
                    onChange={(e) => handleChange("salePrice", e.target.value)}
                    className="w-full border p-2 rounded"
                  />
                </div>
                <div>
                  <Editor
                    className="w-full "
                    value={text}
                    onTextChange={(e) => setText(e.htmlValue ?? "")}
                    style={{ height: "320px" }}
                  />
                </div>
              </div>
            </div>
            <div>
              <div>
                <label className="block font-medium mb-3">Card Image </label>
                <input
                  type="text"
                  placeholder="Product Name"
                  value={product.}
                  onChange={(e) => handleChange("", e.target.value)}
                  className="w-full border p-2 rounded py-4"
                />
              </div>
            </div>

            <div className="col-span-2">
              <div className="max-w-xl mx-auto">
                <button
                  type="submit"
                  className="mt-4 w-full  bg-slate-900 rounded-full text-white py-3  hover:bg-[#0fabca]/90"
                >
                  Submit Product
                </button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default NewProduct;
