/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Card, CardContent } from "@/components/ui/card";
import axios from "axios";
import Link from "next/link";
import { Editor } from "primereact/editor";
import { useState } from "react";

// Define types for better type safety
interface ImageUrl {
  id: string;
  imageName: string;
  url: string;
}

interface Product {
  name: string;
  shortDescription: string;
  description: string; // Rich text description
  price: string;
  salePrice: string;
  imgUrl: ImageUrl[]; // Dynamic product images
  descriptionImages: {
    image1: string;
    image2: string;
  }; // Static description images
  isSale: boolean;
  isHaveCoupon: boolean;
  couponCode?: string;
}
const NewProduct = () => {
  const [text, setText] = useState("");
  const [loading, setLoading] = useState(false);
  const [product, setProduct] = useState<Product>({
    name: "",
    shortDescription: "",
    description: "",
    price: "",
    salePrice: "",
    imgUrl: [
      {
        id: crypto.randomUUID(),
        imageName: "",
        url: "",
      },
    ],
    descriptionImages: {
      image1: "",
      image2: "",
    },
    isSale: false,
    isHaveCoupon: false,
    couponCode: "",
  });

  const handleChange = (field: string, value: any) => {
    setProduct((prev) => ({ ...prev, [field]: value }));
  };

  // Handle image URL changes
  const handleImageUrlChange = (index: number, value: string) => {
    setProduct((prev) => ({
      ...prev,
      imgUrl: prev.imgUrl.map((img, i) =>
        i === index
          ? { ...img, url: value, imageName: `Image ${index + 1}` }
          : img
      ),
    }));
  };

  // Add new image URL input
  const addImageUrl = () => {
    setProduct((prev) => ({
      ...prev,
      imgUrl: [
        ...prev.imgUrl,
        {
          id: crypto.randomUUID(),
          imageName: `Image ${prev.imgUrl.length + 1}`,
          url: "",
        },
      ],
    }));
  };

  // Remove image URL input
  const removeImageUrl = (index: number) => {
    if (product.imgUrl.length > 1) {
      setProduct((prev) => ({
        ...prev,
        imgUrl: prev.imgUrl.filter((_, i) => i !== index),
      }));
    }
  };

  // Handle description image URL changes
  const handleDescriptionImageChange = (
    imageKey: "image1" | "image2",
    value: string
  ) => {
    setProduct((prev) => ({
      ...prev,
      descriptionImages: {
        ...prev.descriptionImages,
        [imageKey]: value,
      },
    }));
  };

  // Handle radio button changes
  const handleRadioChange = (
    field: "isSale" | "isHaveCoupon",
    value: boolean
  ) => {
    setProduct((prev) => ({
      ...prev,
      [field]: value,
      // Clear coupon code if isHaveCoupon is set to false
      ...(field === "isHaveCoupon" && !value && { couponCode: "" }),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate required fields
      if (!product.name || !product.shortDescription || !text) {
        alert("Please fill in all required fields");
        setLoading(false);
        return;
      }

      // Validate character limits
      if (product.name.length > 100) {
        alert("Product name cannot exceed 100 characters");
        setLoading(false);
        return;
      }

      if (product.shortDescription.length > 500) {
        alert("Short description cannot exceed 500 characters");
        setLoading(false);
        return;
      }

      if (!product.price || !product.salePrice) {
        alert("Please enter valid prices");
        setLoading(false);
        return;
      }

      if (parseFloat(product.salePrice) > parseFloat(product.price)) {
        alert("Sale price cannot be greater than regular price");
        setLoading(false);
        return;
      }

      if (product.imgUrl.some((img) => !img.url)) {
        alert("Please provide all product image URLs");
        setLoading(false);
        return;
      }

      if (
        !product.descriptionImages.image1 ||
        !product.descriptionImages.image2
      ) {
        alert("Please provide both description image URLs");
        setLoading(false);
        return;
      }

      if (product.isHaveCoupon && !product.couponCode) {
        alert("Please provide coupon code when coupon is enabled");
        setLoading(false);
        return;
      }

      // Create complete product object for database submission
      const completeProductData = {
        ...product,
        description: text, // Add rich text description to the product object
        price: parseFloat(product.price),
        salePrice: parseFloat(product.salePrice),
      };

      // Submit to API using axios
      const response = await axios.post("/api/product", completeProductData, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.data.success) {
        alert("Product created successfully!");
        console.log("Product created:", response.data.data);

        // Reset form
        setProduct({
          name: "",
          shortDescription: "",
          description: "",
          price: "",
          salePrice: "",
          imgUrl: [
            {
              id: crypto.randomUUID(),
              imageName: "",
              url: "",
            },
          ],
          descriptionImages: {
            image1: "",
            image2: "",
          },
          isSale: false,
          isHaveCoupon: false,
          couponCode: "",
        });
        setText("");
      } else {
        alert(`Error: ${response.data.message}`);
      }
    } catch (error: any) {
      console.error("Error creating product:", error);

      if (error.response?.data?.message) {
        alert(`Error: ${error.response.data.message}`);
      } else if (error.response?.data?.errors) {
        alert(`Validation Error: ${error.response.data.errors.join(", ")}`);
      } else {
        alert("Failed to create product. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="">
      <div className="flex justify-between items-center mb-5">
        <h1 className="text-2xl font-semibold mb-4">Add New Product</h1>
        <Link href={"/admin/products"}>
          <button className="bg-black text-white px-4 py-2 rounded-md">
            Back to Products
          </button>
        </Link>
      </div>
      <Card className="py-5">
        <CardContent>
          <form
            onSubmit={handleSubmit}
            className="grid grid-cols-1 lg:grid-cols-2 gap-4"
          >
            <div>
              <div className="space-y-4">
                <div>
                  <label className="block font-medium mb-3">
                    Product Name
                    <span
                      className={`text-sm ml-2 ${
                        product.name.length > 100
                          ? "text-red-500"
                          : "text-gray-500"
                      }`}
                    >
                      ({product.name.length}/100 characters)
                    </span>
                  </label>
                  <input
                    type="text"
                    placeholder="Product Name (max 100 characters)"
                    value={product.name}
                    onChange={(e) => handleChange("name", e.target.value)}
                    className={`w-full border p-2 rounded py-4 ${
                      product.name.length > 100 ? "border-red-500" : ""
                    }`}
                    maxLength={100}
                  />
                  {product.name.length > 100 && (
                    <p className="text-red-500 text-sm mt-1">
                      Product name cannot exceed 100 characters
                    </p>
                  )}
                </div>
                <div>
                  <label className="block font-medium mb-3">
                    Short Product Description
                    <span
                      className={`text-sm ml-2 ${
                        product.shortDescription.length > 500
                          ? "text-red-500"
                          : "text-gray-500"
                      }`}
                    >
                      ({product.shortDescription.length}/500 characters)
                    </span>
                  </label>
                  <textarea
                    placeholder="Product Description (max 500 characters)"
                    value={product.shortDescription}
                    onChange={(e) =>
                      handleChange("shortDescription", e.target.value)
                    }
                    className={`w-full border p-2 rounded min-h-16 ${
                      product.shortDescription.length > 500
                        ? "border-red-500"
                        : ""
                    }`}
                    maxLength={500}
                  />
                  {product.shortDescription.length > 500 && (
                    <p className="text-red-500 text-sm mt-1">
                      Short description cannot exceed 500 characters
                    </p>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <input
                    type="number"
                    placeholder="Price"
                    value={product.price}
                    onChange={(e) => handleChange("price", e.target.value)}
                    className="w-full border p-2 rounded"
                  />
                  <input
                    type="number"
                    placeholder="Sale Price"
                    value={product.salePrice}
                    onChange={(e) => handleChange("salePrice", e.target.value)}
                    className="w-full border p-2 rounded"
                  />
                </div>
                <div>
                  <Editor
                    className="w-full "
                    value={text}
                    onTextChange={(e) => setText(e.htmlValue ?? "")}
                    style={{ height: "320px" }}
                  />
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block font-medium mb-3">Product Images</label>
                <div className="space-y-3">
                  {product.imgUrl.map((img, index) => (
                    <div key={img.id} className="flex items-center gap-2">
                      <input
                        type="text"
                        placeholder={`Image URL ${index + 1}`}
                        value={img.url}
                        onChange={(e) =>
                          handleImageUrlChange(index, e.target.value)
                        }
                        className="flex-1 border p-2 rounded py-4"
                      />
                      {product.imgUrl.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeImageUrl(index)}
                          className="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                          title="Remove image"
                        >
                          ✕
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addImageUrl}
                    className="w-full py-2 px-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-800 transition-colors"
                  >
                    + Add Another Image URL
                  </button>
                </div>
              </div>
              <div className="border px-5 py-3 shadow rounded-md">
                <div className="space-y-4">
                  {/* Sale Option */}
                  <div>
                    <label className="block font-medium mb-3">
                      Is this product on sale?
                    </label>
                    <div className="flex gap-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isSale"
                          checked={product.isSale === true}
                          onChange={() => handleRadioChange("isSale", true)}
                          className="mr-2"
                        />
                        Yes
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isSale"
                          checked={product.isSale === false}
                          onChange={() => handleRadioChange("isSale", false)}
                          className="mr-2"
                        />
                        No
                      </label>
                    </div>
                  </div>

                  {/* Coupon Option */}
                  <div>
                    <label className="block font-medium mb-3">
                      Does this product have a coupon?
                    </label>
                    <div className="flex gap-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isHaveCoupon"
                          checked={product.isHaveCoupon === true}
                          onChange={() =>
                            handleRadioChange("isHaveCoupon", true)
                          }
                          className="mr-2"
                        />
                        Yes
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="isHaveCoupon"
                          checked={product.isHaveCoupon === false}
                          onChange={() =>
                            handleRadioChange("isHaveCoupon", false)
                          }
                          className="mr-2"
                        />
                        No
                      </label>
                    </div>

                    {/* Coupon Code Input - Only show if isHaveCoupon is true */}
                    {product.isHaveCoupon && (
                      <div className="mt-3">
                        <input
                          type="text"
                          placeholder="Enter coupon code"
                          value={product.couponCode || ""}
                          onChange={(e) =>
                            handleChange("couponCode", e.target.value)
                          }
                          className="w-full border p-2 rounded py-4"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Description Images Section */}
            <div>
              <div>
                <label className="block font-medium mb-3">
                  Card Images (Exactly 2 Images)
                </label>
                <div className="space-y-3">
                  <input
                    type="text"
                    placeholder="Description Image 1 URL"
                    value={product.descriptionImages.image1}
                    onChange={(e) =>
                      handleDescriptionImageChange("image1", e.target.value)
                    }
                    className="w-full border p-2 rounded py-4"
                  />
                  <input
                    type="text"
                    placeholder="Description Image 2 URL"
                    value={product.descriptionImages.image2}
                    onChange={(e) =>
                      handleDescriptionImageChange("image2", e.target.value)
                    }
                    className="w-full border p-2 rounded py-4"
                  />
                </div>
              </div>
            </div>

            {/* Sale and Coupon Options */}

            <div className="col-span-2">
              <div className="max-w-xl mx-auto">
                <button
                  type="submit"
                  disabled={loading}
                  className={`mt-4 w-full rounded-full text-white py-3 transition-colors ${
                    loading
                      ? "bg-gray-400 cursor-not-allowed"
                      : "bg-slate-900 hover:bg-[#0fabca]/90"
                  }`}
                >
                  {loading ? "Creating Product..." : "Submit Product"}
                </button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default NewProduct;
