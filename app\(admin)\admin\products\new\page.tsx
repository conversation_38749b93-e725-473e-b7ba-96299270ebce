/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { Editor } from "primereact/editor";
import { useState } from "react";

// Define types for better type safety
interface ImageUrl {
  id: string;
  imageName: string;
  url: string;
}

interface Product {
  name: string;
  shortDescription: string;
  price: string;
  salePrice: string;
  imgUrl: ImageUrl[];
}
const NewProduct = () => {
  const [text, setText] = useState("");
  const [product, setProduct] = useState<Product>({
    name: "",
    shortDescription: "",
    price: "",
    salePrice: "",
    imgUrl: [
      {
        id: crypto.randomUUID(),
        imageName: "",
        url: "",
      },
    ],
  });

  const handleChange = (field: string, value: any) => {
    setProduct((prev) => ({ ...prev, [field]: value }));
  };

  // Handle image URL changes
  const handleImageUrlChange = (index: number, value: string) => {
    setProduct((prev) => ({
      ...prev,
      imgUrl: prev.imgUrl.map((img, i) =>
        i === index
          ? { ...img, url: value, imageName: `Image ${index + 1}` }
          : img
      ),
    }));
  };

  // Add new image URL input
  const addImageUrl = () => {
    setProduct((prev) => ({
      ...prev,
      imgUrl: [
        ...prev.imgUrl,
        {
          id: crypto.randomUUID(),
          imageName: `Image ${prev.imgUrl.length + 1}`,
          url: "",
        },
      ],
    }));
  };

  // Remove image URL input
  const removeImageUrl = (index: number) => {
    if (product.imgUrl.length > 1) {
      setProduct((prev) => ({
        ...prev,
        imgUrl: prev.imgUrl.filter((_, i) => i !== index),
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Product Data:", product, text);
  };

  return (
    <div className="">
      <div className="flex justify-between items-center mb-5">
        <h1 className="text-2xl font-semibold mb-4">Add New Product</h1>
        <Link href={"/admin/products"}>
          <button className="bg-black text-white px-4 py-2 rounded-md">
            Back to Products
          </button>
        </Link>
      </div>
      <Card className="py-5">
        <CardContent>
          <form
            onSubmit={handleSubmit}
            className="grid grid-cols-1 lg:grid-cols-2 gap-4"
          >
            <div>
              <div className="space-y-4">
                <div>
                  <label className="block font-medium mb-3">Product Name</label>
                  <input
                    type="text"
                    placeholder="Product Name"
                    value={product.name}
                    onChange={(e) => handleChange("name", e.target.value)}
                    className="w-full border p-2 rounded py-4"
                  />
                </div>
                <div>
                  <label className="block font-medium mb-3">
                    Short Product Description
                  </label>
                  <textarea
                    placeholder="Product Description"
                    value={product.shortDescription}
                    onChange={(e) =>
                      handleChange("shortDescription", e.target.value)
                    }
                    className="w-full border p-2 rounded min-h-16"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <input
                    type="number"
                    placeholder="Price"
                    value={product.price}
                    onChange={(e) => handleChange("price", e.target.value)}
                    className="w-full border p-2 rounded"
                  />
                  <input
                    type="number"
                    placeholder="Sale Price"
                    value={product.salePrice}
                    onChange={(e) => handleChange("salePrice", e.target.value)}
                    className="w-full border p-2 rounded"
                  />
                </div>
                <div>
                  <Editor
                    className="w-full "
                    value={text}
                    onTextChange={(e) => setText(e.htmlValue ?? "")}
                    style={{ height: "320px" }}
                  />
                </div>
              </div>
            </div>
            <div>
              <div>
                <label className="block font-medium mb-3">Product Images</label>
                <div className="space-y-3">
                  {product.imgUrl.map((img, index) => (
                    <div key={img.id} className="flex items-center gap-2">
                      <input
                        type="text"
                        placeholder={`Image URL ${index + 1}`}
                        value={img.url}
                        onChange={(e) =>
                          handleImageUrlChange(index, e.target.value)
                        }
                        className="flex-1 border p-2 rounded py-4"
                      />
                      {product.imgUrl.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeImageUrl(index)}
                          className="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                          title="Remove image"
                        >
                          ✕
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addImageUrl}
                    className="w-full py-2 px-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-800 transition-colors"
                  >
                    + Add Another Image URL
                  </button>
                </div>
              </div>
            </div>

            <div className="col-span-2">
              <div className="max-w-xl mx-auto">
                <button
                  type="submit"
                  className="mt-4 w-full  bg-slate-900 rounded-full text-white py-3  hover:bg-[#0fabca]/90"
                >
                  Submit Product
                </button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default NewProduct;
