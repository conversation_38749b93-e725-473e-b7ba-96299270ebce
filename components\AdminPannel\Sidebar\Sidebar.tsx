"use client";
import LogoComp from "@/components/logo/logo";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { BsCart2 } from "react-icons/bs";
import { CiSettings } from "react-icons/ci";
import { GoCodeReview } from "react-icons/go";
import { IoIosArrowDropright } from "react-icons/io";
import { IoImageOutline } from "react-icons/io5";
import { LuUsers } from "react-icons/lu";
import { MdOutlineCategory, MdOutlineDashboard } from "react-icons/md";
import { PiTShirt } from "react-icons/pi";
import { RiCoupon2Line } from "react-icons/ri";
import { TbBrandAdobe } from "react-icons/tb";
const sidebarMenu = [
  {
    name: "Dashboard",
    href: "/admin/dashboard",
    icon: <MdOutlineDashboard className="size-6" />,
  },
  {
    name: "Products",
    href: "/admin/products",
    icon: <PiTShirt className="size-6" />,
  },
  {
    name: "Orders",
    href: "/admin/orders",
    icon: <BsCart2 className="size-6" />,
  },
  {
    name: "Categories",
    href: "/admin/categories",
    icon: <MdOutlineCategory className="size-6" />,
  },
  {
    name: "Brands",
    href: "/admin/brands",
    icon: <TbBrandAdobe className="size-6" />,
  },
  {
    name: "Coupons",
    href: "/admin/coupons",
    icon: <RiCoupon2Line className="size-6" />,
  },
  {
    name: "Banners",
    href: "/admin/banners",
    icon: <IoImageOutline className="size-6" />,
  },
  {
    name: "Reviews",
    href: "/admin/reviews",
    icon: <GoCodeReview className="size-6" />,
  },
  {
    name: "Customers",
    href: "/admin/customers",
    icon: <LuUsers className="size-6" />,
  },
  {
    name: "Settings",
    href: "/admin/settings",
    icon: <CiSettings className="size-6" />,
  },
];
const Sidebar = () => {
  const path = usePathname();

  const [isOpen, setIsOpen] = useState(true);

  return (
    <>
      <aside
        onClick={() => {
          if (!isOpen) {
            setIsOpen(true);
          }
        }}
        className={`h-screen  ${
          isOpen ? "max-w-[250px]" : "max-w-[80px]"
        } p-5 w-full relative border-r shadow-md overflow-y-auto overflow-x-hidden transition-all duration-300`}
      >
        {isOpen ? (
          <button
            className="absolute right-0 top-0 p-3"
            onClick={() => {
              setIsOpen(false);
            }}
          >
            <IoIosArrowDropright className="size-6" />
          </button>
        ) : null}

        <div className="flex  gap-2 items-center justify-between w-full   pt-5 pb-10">
          {isOpen ? (
            <LogoComp />
          ) : (
            <div className="w-10 h-10 flex justify-center items-center p-5 text-white bg-green-500 rounded-full">
              L
            </div>
          )}
        </div>
        <ul className="space-y-2">
          {sidebarMenu.map((item, index) => (
            <li key={index}>
              <Link
                className={`${
                  path === item.href && isOpen ? "bg-green-100" : ""
                } 
                ${
                  isOpen
                    ? "justify-start py-3  pl-5"
                    : "justify-center pl-0 py-3"
                }  
                w-full   flex gap-2 items-center  rounded-xl font-medium  `}
                href={item.href}
              >
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>{item.icon}</TooltipTrigger>
                    <TooltipContent side="right">
                      <p>{item.name}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <span className={`${!isOpen ? "hidden" : "block"} `}>
                  {item.name}
                </span>
              </Link>
            </li>
          ))}
        </ul>
      </aside>
    </>
  );
};

export default Sidebar;
