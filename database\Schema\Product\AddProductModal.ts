import mongoose, { Document, Schema } from "mongoose";

// Define interface for ImageUrl
interface IImageUrl {
  id: string;
  imageName: string;
  url: string;
}

// Define interface for Description Images
interface IDescriptionImages {
  image1: string;
  image2: string;
}

// Define interface for Product document
export interface IProduct extends Document {
  name: string;
  shortDescription: string;
  description: string; // Rich text description
  price: number;
  salePrice: number;
  imgUrl: IImageUrl[]; // Dynamic product images
  descriptionImages: IDescriptionImages; // Static description images
  isSale: boolean;
  isHaveCoupon: boolean;
  couponCode?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Define schema for ImageUrl subdocument
const ImageUrlSchema = new Schema<IImageUrl>(
  {
    id: {
      type: String,
      required: true,
    },
    imageName: {
      type: String,
      required: true,
    },
    url: {
      type: String,
      required: true,
      validate: {
        validator: function (v: string) {
          // Basic URL validation
          return /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/.test(
            v
          );
        },
        message: "Please enter a valid URL",
      },
    },
  },
  { _id: false }
);

// Define schema for Description Images subdocument
const DescriptionImagesSchema = new Schema<IDescriptionImages>(
  {
    image1: {
      type: String,
      required: true,
      validate: {
        validator: function (v: string) {
          return /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/.test(
            v
          );
        },
        message: "Please enter a valid URL for image 1",
      },
    },
    image2: {
      type: String,
      required: true,
      validate: {
        validator: function (v: string) {
          return /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/.test(
            v
          );
        },
        message: "Please enter a valid URL for image 2",
      },
    },
  },
  { _id: false }
);

// Define main Product schema
const ProductSchema = new Schema<IProduct>(
  {
    name: {
      type: String,
      required: [true, "Product name is required"],
      trim: true,
      maxlength: [100, "Product name cannot exceed 100 characters"],
    },
    shortDescription: {
      type: String,
      required: [true, "Short description is required"],
      trim: true,
      maxlength: [10000, "Short description cannot exceed 10000 characters"],
    },
    description: {
      type: String,
      required: [true, "Product description is required"],
      trim: true,
    },
    price: {
      type: Number,
      required: [true, "Product price is required"],
      min: [0, "Price cannot be negative"],
      validate: {
        validator: function (v: number) {
          return v > 0;
        },
        message: "Price must be greater than 0",
      },
    },
    salePrice: {
      type: Number,
      required: [true, "Sale price is required"],
      min: [0, "Sale price cannot be negative"],
      validate: {
        validator: function (this: IProduct, v: number) {
          return v <= this.price;
        },
        message: "Sale price cannot be greater than regular price",
      },
    },
    imgUrl: {
      type: [ImageUrlSchema],
      required: [true, "At least one product image is required"],
      validate: {
        validator: function (v: IImageUrl[]) {
          return v.length > 0;
        },
        message: "At least one product image is required",
      },
    },
    descriptionImages: {
      type: DescriptionImagesSchema,
      required: [true, "Description images are required"],
    },
    isSale: {
      type: Boolean,
      required: true,
      default: false,
    },
    isHaveCoupon: {
      type: Boolean,
      required: true,
      default: false,
    },
    couponCode: {
      type: String,
      trim: true,
      validate: {
        validator: function (this: IProduct, v: string) {
          // If isHaveCoupon is true, couponCode is required
          if (this.isHaveCoupon && (!v || v.trim() === "")) {
            return false;
          }
          return true;
        },
        message: "Coupon code is required when product has coupon",
      },
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Add indexes for better query performance
ProductSchema.index({ name: 1 });
ProductSchema.index({ isSale: 1 });
ProductSchema.index({ isHaveCoupon: 1 });
ProductSchema.index({ price: 1 });
ProductSchema.index({ createdAt: -1 });

// Add virtual for formatted price
ProductSchema.virtual("formattedPrice").get(function () {
  return `$${this.price.toFixed(2)}`;
});

ProductSchema.virtual("formattedSalePrice").get(function () {
  return `$${this.salePrice.toFixed(2)}`;
});

// Add virtual for discount percentage
ProductSchema.virtual("discountPercentage").get(function () {
  if (this.isSale && this.salePrice < this.price) {
    return Math.round(((this.price - this.salePrice) / this.price) * 100);
  }
  return 0;
});

// Pre-save middleware to ensure data consistency
ProductSchema.pre("save", function (next) {
  // If isHaveCoupon is false, clear couponCode
  if (!this.isHaveCoupon) {
    this.couponCode = undefined;
  }

  // Ensure salePrice is not greater than price
  if (this.salePrice > this.price) {
    this.salePrice = this.price;
  }

  next();
});

// Create and export the model
const Product =
  mongoose.models.Product || mongoose.model<IProduct>("Product", ProductSchema);

export default Product;
