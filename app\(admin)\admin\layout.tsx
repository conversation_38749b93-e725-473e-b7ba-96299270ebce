import Sidebar from "@/components/AdminPannel/Sidebar/Sidebar";
import SidebarNav from "@/components/AdminPannel/Sidebar/SidebarNav";
import { ScrollArea } from "@/components/ui/scroll-area";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "LumeAxis Admin Panel",
  description: "Generated by Mioxdropic",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <div className="flex items-start ">
        <Sidebar />
        <div className="flex flex-col w-full flex-1">
          <SidebarNav />
          <ScrollArea className="h-[calc(100vh-80px)]">
            <main className="flex-1 px-5 py-5 dark:bg-gray-950">
              {children}
            </main>
          </ScrollArea>
        </div>
      </div>
    </>
  );
}
