# 🕰️ LumeAxis - Premium Watch E-commerce Platform

A modern, full-stack e-commerce platform built with Next.js 15, specializing in luxury watches and timepieces. Features a comprehensive admin panel, customer-facing storefront, and robust product management system.

## 🚀 Features

### 🛍️ Customer Features

- **Modern Storefront**: Responsive design with smooth animations using GSAP and Lenis
- **Product Catalog**: Browse watches with advanced filtering and search
- **Shopping Cart**: Redux-powered cart management with persistent state
- **User Authentication**: Secure login/register with NextAuth.js
- **Product Details**: Rich product pages with image galleries and descriptions
- **Checkout Process**: Streamlined checkout with order management
- **User Profile**: Order history, account settings, and profile management
- **Responsive Design**: Mobile-first approach with Tailwind CSS

### 🔧 Admin Features

- **Complete Dashboard**: Comprehensive admin panel with sidebar navigation
- **Product Management**: Full CRUD operations for products
  - Create products with rich text descriptions
  - Dynamic image URL management
  - Price and sale price configuration
  - Coupon system integration
  - Bulk operations support
- **Order Management**: Track and manage customer orders
- **Category Management**: Organize products by categories
- **Brand Management**: Manage watch brands and manufacturers
- **Customer Management**: View and manage customer accounts
- **Coupon System**: Create and manage discount coupons
- **Banner Management**: Control homepage banners and promotions
- **Review System**: Moderate customer reviews
- **Settings Panel**: Configure platform settings

### 🛠️ Technical Features

- **Type-Safe**: Full TypeScript implementation
- **Database**: MongoDB with Mongoose ODM
- **API Routes**: RESTful API with Next.js App Router
- **State Management**: Redux Toolkit + Zustand
- **UI Components**: Radix UI + shadcn/ui components
- **Styling**: Tailwind CSS with custom animations
- **Form Handling**: React Hook Form with validation
- **File Upload**: Cloudinary integration
- **Rich Text Editor**: PrimeReact Editor for product descriptions
- **Authentication**: NextAuth.js with Firebase integration
- **Package Manager**: Bun for fast dependency management

## 🏗️ Project Structure

```text
vendor-full-stack-v1/
├── app/                          # Next.js App Router
│   ├── (admin)/                  # Admin panel routes
│   │   └── admin/
│   │       ├── products/         # Product management
│   │       ├── orders/           # Order management
│   │       ├── categories/       # Category management
│   │       ├── brands/           # Brand management
│   │       ├── customers/        # Customer management
│   │       ├── coupons/          # Coupon management
│   │       ├── banners/          # Banner management
│   │       ├── reviews/          # Review management
│   │       └── settings/         # Settings panel
│   ├── (auth)/                   # Authentication routes
│   │   ├── login/
│   │   └── register/
│   ├── (web)/                    # Customer-facing routes
│   │   ├── products/             # Product catalog
│   │   ├── cart/                 # Shopping cart
│   │   ├── checkout/             # Checkout process
│   │   ├── profile/              # User profile
│   │   └── discount/             # Discount pages
│   └── api/                      # API routes
│       ├── product/              # Product API endpoints
│       └── order/                # Order API endpoints
├── components/                   # Reusable components
│   ├── AdminPannel/              # Admin-specific components
│   ├── ui/                       # shadcn/ui components
│   ├── Navbar/                   # Navigation components
│   ├── ProductCard/              # Product display components
│   ├── Cart/                     # Shopping cart components
│   ├── Hero/                     # Homepage sections
│   └── ...                       # Other feature components
├── database/                     # Database configuration
│   ├── Schema/                   # Mongoose schemas
│   │   ├── Product/              # Product schema
│   │   └── Order/                # Order schema
│   └── db.config.ts              # Database connection
├── Store/                        # Redux store configuration
│   ├── Features/                 # Redux slices
│   └── Provider/                 # Store provider
├── hooks/                        # Custom React hooks
├── lib/                          # Utility libraries
├── Types/                        # TypeScript type definitions
└── public/                       # Static assets
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ or Bun
- MongoDB (local or cloud)
- Git

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/vendor-full-stack-v1.git
   cd vendor-full-stack-v1
   ```

2. **Install dependencies**

   ```bash
   bun install
   # or
   npm install
   ```

3. **Environment Setup**
   Create a `.env.local` file in the root directory:

   ```env
   # Database
   MONGO_URL_ONLINE=your_mongodb_connection_string

   # NextAuth
   NEXTAUTH_SECRET=your_nextauth_secret
   NEXTAUTH_URL=http://localhost:3000

   # Firebase (if using)
   FIREBASE_API_KEY=your_firebase_api_key
   FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
   FIREBASE_PROJECT_ID=your_firebase_project_id

   # Cloudinary (if using)
   CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
   CLOUDINARY_API_KEY=your_cloudinary_api_key
   CLOUDINARY_API_SECRET=your_cloudinary_api_secret
   ```

4. **Start the development server**

   ```bash
   bun dev
   # or
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📱 Usage

### Customer Interface

- **Homepage**: Browse featured products and brands
- **Product Catalog**: Filter and search through watch collections
- **Product Details**: View detailed product information and images
- **Shopping Cart**: Add/remove items and proceed to checkout
- **User Account**: Register, login, and manage profile

### Admin Interface

- **Access**: Navigate to `/admin` (authentication required)
- **Dashboard**: Overview of platform statistics
- **Product Management**: Create, edit, and delete products
- **Order Processing**: View and manage customer orders
- **Content Management**: Manage categories, brands, and banners

## 🛠️ Tech Stack

### Frontend

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI + shadcn/ui
- **Animations**: GSAP + Lenis smooth scrolling
- **State Management**: Redux Toolkit + Zustand
- **Forms**: React Hook Form

### Backend

- **Runtime**: Node.js
- **Database**: MongoDB with Mongoose
- **Authentication**: NextAuth.js
- **File Upload**: Cloudinary
- **API**: RESTful with Next.js API routes

### Development Tools

- **Package Manager**: Bun
- **Linting**: ESLint
- **Type Checking**: TypeScript
- **Build Tool**: Next.js built-in

## 🔧 API Endpoints

### Product Management

- `GET /api/product` - Get all products (with pagination and filtering)
- `GET /api/product?id={id}` - Get single product
- `POST /api/product` - Create new product
- `PUT /api/product?id={id}` - Update product
- `DELETE /api/product?id={id}` - Delete product
- `PATCH /api/product` - Bulk operations

### Order Management

- `GET /api/order` - Get orders
- `POST /api/order` - Create order
- `PUT /api/order?id={id}` - Update order status

## 🎨 Design System

The project uses a consistent design system built with:

- **Colors**: Custom color palette with dark mode support
- **Typography**: Poppins font family with multiple weights
- **Components**: Reusable UI components with variants
- **Animations**: Smooth transitions and micro-interactions
- **Responsive**: Mobile-first responsive design

## 🚀 Deployment

### Build for Production

```bash
bun run build
```

### Start Production Server

```bash
bun start
```

### Environment Variables for Production

Ensure all environment variables are properly set in your production environment.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Mahamudul Hasan Miyad**

- GitHub: [@mioxdropic](https://github.com/mioxdropic)
- Email: <EMAIL>

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Radix UI for accessible components
- Tailwind CSS for utility-first styling
- MongoDB for the database solution
- All open-source contributors

---

**Built with ❤️ using Next.js, TypeScript, and modern web technologies**
