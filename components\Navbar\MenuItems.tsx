/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { Heart, Search, ShoppingCart, User } from "lucide-react";
import Link from "next/link";
import { ProfileDropDown } from "../ProfileComponent/ProfileDropDown";
import { Button } from "../ui/button";
const MenuItems = ({ user }: { user: any }) => {
  // Get cart count from Redux state
  const cartState = useAppSelector((state) => state.cart);
  const cartCount = cartState.totalQuantity;

  return (
    <>
      <Sheet>
        <div className="flex items-center justify-between px-4 py-3 bg-white">
          {/* Search Bar */}
          <div className="relative flex-1 max-w-sm mx-auto hidden lg:block">
            <div className="relative flex items-center">
              <Search className="absolute left-3 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search"
                className="w-full pl-10 pr-4 py-2 bg-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Navigation Icons */}
          <div className="flex items-center gap-2 md:gap-4 ml-4">
            <Link
              href={"/profile"}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              {user ? (
                <ProfileDropDown img={user?.photoURL || ""} />
              ) : (
                <User className="w-6 h-6 text-gray-700" />
              )}
            </Link>

            <button className="p-2 hover:bg-gray-100 rounded-full">
              <Heart className="w-6 h-6 text-gray-700" />
            </button>
            <div>
              <Link href={"/cart"} className="hidden lg:block">
                <button className="p-2 hover:bg-gray-100 rounded-full relative">
                  <ShoppingCart className="w-6 h-6 text-gray-700" />
                  {cartCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                      {cartCount}
                    </span>
                  )}
                </button>
              </Link>
              <SheetTrigger asChild className="block lg:hidden">
                <button className="p-2 hover:bg-gray-100 rounded-full relative">
                  <ShoppingCart className="w-6 h-6 text-gray-700" />
                  {cartCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                      {cartCount}
                    </span>
                  )}
                </button>
              </SheetTrigger>
            </div>
          </div>
        </div>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Edit profile</SheetTitle>
            <SheetDescription>
              Make changes to your profile here. Click save when {`you're`}{" "}
              done.
            </SheetDescription>
          </SheetHeader>
          <div>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae
            dicta suscipit pariatur reiciendis sapiente similique exercitationem
            rem praesentium et culpa a, ducimus, magni magnam quod molestiae sit
            recusandae omnis quas?
          </div>
          <SheetFooter>
            <SheetClose asChild>
              <Button type="submit">Save changes</Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </>
  );
};

export default MenuItems;
