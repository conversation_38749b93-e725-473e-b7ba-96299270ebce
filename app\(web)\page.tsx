import BenefitsBanner from "@/components/Hero/BenifitBanner";
import BrandShow from "@/components/Hero/BrandShow";
import HeroSection from "@/components/Hero/HeroSection";
import ProductSection from "@/components/ProductCard/ProductSection";
import TrendingSection from "@/components/ProductCard/TrendingSection";
import PrivacyCard from "@/components/StackCard/PrivacyCard";
import ProductTrending from "@/components/StackCard/ProductTrending";
import StackCard from "@/components/StackCard/StackCard";
import Image from "next/image";
import Link from "next/link";

const Home = () => {
  return (
    <>
      <section>
        <HeroSection />
      </section>
      <div className="mb-10">
        <BenefitsBanner />
      </div>
      <section>
        <div className="grid grid-cols-1 lg:grid-cols-2 px-5 lg:px-10 gap-5">
          <div className="max-h-[700px] h-full ">
            <Image
              src={
                "https://www.justwatches.com/cdn/shop/files/640_X_620_3fc186b1-2665-4c3f-9628-71d374b46ebd.png?v=1738129689&width=1000"
              }
              alt="img-1"
              width={1000}
              height={1000}
              quality={100}
              className="object-cover w-full h-full aspect-square"
            />
          </div>
          <div className="max-h-[700px] h-full ">
            <Image
              src={
                "https://www.justwatches.com/cdn/shop/files/640_X_620_3e600301-6a12-4bdb-ba88-d17bb93b90fa.png?v=1738129748&width=1000"
              }
              alt="img-1"
              width={1000}
              height={1000}
              quality={100}
              className="object-cover w-full h-full aspect-square"
            />
          </div>
        </div>
      </section>
      <section>
        {/* New Arrivals  */}
        <div className="mt-20 mb-10 px-5 lg:px-10">
          <ProductSection title={"New Arrivals"} type="newArrivals" />
        </div>
      </section>
      <section>
        {" "}
        <div className="mt-20 mb-10 px-5 lg:px-10">
          <TrendingSection />
        </div>
      </section>
      <section>
        <ProductTrending />
      </section>
      <section>
        <div className="px-5 lg:px-10 py-8">
          <h3 className="text-2xl md:text-4xl lg:text-5xl font-semibold mb-5 ">
            Our Brands{" "}
          </h3>
          <BrandShow />
        </div>
      </section>
      <section>
        <div className="mt-20 mb-10 px-5 lg:px-10">
          <ProductSection title={"Best Sellers"} type="bestSellers" />
        </div>
      </section>
      <section>
        <div className="my-20 px-5 lg:px-10">
          <div className="grid grid-col-1 lg:grid-cols-2 ">
            <div className="bg-[#f4f4f4] flex h-full items-center  justify-center ">
              <div className="w-full lg:max-w-[70%] p-20 lg:p-0">
                <p className="text-sm md:text-lg lg:text-xl xl:text-2xl font-light  capitalize ">
                  Timeless and iconic
                </p>
                <h2 className="text-2xl md:text-3xl lg:text-5xl xl:text-7xl py-4 font-semibold">
                  {" "}
                  VERSACE
                </h2>
                <p className="text-sm md:text-base lg:text-lg pb-5 ">
                  The Versace Watches collection features classic or sporty
                  designs boasting sophisticated functionalities, rich details
                  and flawless style.
                </p>
                <Link
                  href={"/"}
                  className="border justify-center uppercase flex items-center  border-black w-[200px] h-[50px] hover:bg-black transition-all ease-linear duration-200 hover:text-white"
                >
                  Shop Now
                </Link>
              </div>
            </div>
            <div>
              <Image
                src={
                  "https://www.justwatches.com/cdn/shop/files/1600x1600-versace.jpg?v=1712243241&width=1080"
                }
                alt="img-1"
                width={2000}
                height={2000}
                quality={100}
                className="object-cover w-full h-full aspect-square"
              />
            </div>
          </div>
          <h4 className="text-[45px] sm:text-[50px] md:text-[65px] lg:text-[80px] xl:text-[105px] leading-[1.2] 2xl:max-w-[80%] mx-auto text-center font-medium py-10">
            Authentic home of fashion & luxury watch brands.​
          </h4>
        </div>
      </section>
      <section>
        <StackCard />
      </section>
      <section className="h-max">
        <PrivacyCard />
      </section>
    </>
  );
};

export default Home;
