/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { ReactLenis } from "lenis/react";
import React, { useEffect, useRef } from "react";

const LenisScroll = ({ children }: { children: React.ReactNode }) => {
  const lenisRef = useRef<any>(null);

  useEffect(() => {
    const lenis = lenisRef.current?.lenis;

    if (!lenis) return;

    const handleRaf = (time: number) => {
      lenis.raf(time);
      requestAnimationFrame(handleRaf);
    };

    requestAnimationFrame(handleRaf);

    return () => {
      lenis.destroy();
    };
  }, []);

  return (
    <ReactLenis
      root
      options={{
        autoRaf: true, // Enable auto RAF
        duration: 1.2,
        easing: (t: number) => 1 - Math.pow(1 - t, 4),
        lerp: 0.1,
      }}
      ref={lenisRef}
    >
      {children}
    </ReactLenis>
  );
};

export default LenisScroll;
