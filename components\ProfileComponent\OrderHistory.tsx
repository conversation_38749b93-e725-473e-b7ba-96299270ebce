/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function OrderHistory({
  openOrderDetails,
}: {
  openOrderDetails: (order: any) => void;
}) {
  const orders = [
    {
      id: 1,
      number: "ORD001",
      date: "2023-05-01",
      status: "Delivered",
      total: 99.99,
    },
    {
      id: 2,
      number: "ORD002",
      date: "2023-05-15",
      status: "Processing",
      total: 149.99,
    },
    {
      id: 3,
      number: "ORD003",
      date: "2023-06-01",
      status: "Shipped",
      total: 79.99,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Order History</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order Number</TableHead>
              <TableHead>Order Date</TableHead>
              <TableHead>Order Status</TableHead>
              <TableHead>Total Amount</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orders.map((order) => (
              <TableRow key={order.id}>
                <TableCell>{order.number}</TableCell>
                <TableCell>{order.date}</TableCell>
                <TableCell
                  className={`${
                    order.status === "Shipped"
                      ? "text-blue-500 font-bold"
                      : order.status === "Processing"
                      ? "text-yellow-500 font-bold"
                      : order.status === "Delivered"
                      ? "text-green-500 font-bold"
                      : ""
                  }`}
                >
                  {order.status}
                </TableCell>
                <TableCell>${order.total.toFixed(2)}</TableCell>
                <TableCell>
                  <Button
                    variant="outline"
                    onClick={() => openOrderDetails(order)}
                  >
                    View Details
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
