import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import axios from "axios";

// Product interface
export interface Product {
  _id: string;
  name: string;
  price: number;
  salePrice: number;
  description: string;
  imgUrl: Array<{ url: string }>;
  brand: string;
  category: string;
  isSale: boolean;
  isNew: boolean;
  isTrending: boolean;
  stock: number;
  createdAt: string;
  updatedAt: string;
}

// API Response interface
interface ProductsResponse {
  success: boolean;
  message: string;
  data: Product[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalProducts: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// Product state interface
interface ProductState {
  products: Product[];
  newArrivals: Product[];
  trending: Product[];
  bestSellers: Product[];
  loading: boolean;
  error: string | null;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalProducts: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  } | null;
}

// Initial state
const initialState: ProductState = {
  products: [],
  newArrivals: [],
  trending: [],
  bestSellers: [],
  loading: false,
  error: null,
  pagination: null,
};

// Async thunk to fetch all products
export const fetchProducts = createAsyncThunk(
  "products/fetchProducts",
  async (params?: { page?: number; limit?: number; category?: string; brand?: string }) => {
    try {
      const queryParams = new URLSearchParams();
      
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.category) queryParams.append("category", params.category);
      if (params?.brand) queryParams.append("brand", params.brand);

      const response = await axios.get<ProductsResponse>(`/api/product?${queryParams.toString()}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to fetch products");
    }
  }
);

// Async thunk to fetch new arrivals
export const fetchNewArrivals = createAsyncThunk(
  "products/fetchNewArrivals",
  async () => {
    try {
      const response = await axios.get<ProductsResponse>("/api/product?isNew=true&limit=8");
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to fetch new arrivals");
    }
  }
);

// Async thunk to fetch trending products
export const fetchTrending = createAsyncThunk(
  "products/fetchTrending",
  async () => {
    try {
      const response = await axios.get<ProductsResponse>("/api/product?isTrending=true&limit=8");
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to fetch trending products");
    }
  }
);

// Async thunk to fetch best sellers
export const fetchBestSellers = createAsyncThunk(
  "products/fetchBestSellers",
  async () => {
    try {
      const response = await axios.get<ProductsResponse>("/api/product?limit=8&sortBy=createdAt&sortOrder=desc");
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to fetch best sellers");
    }
  }
);

// Async thunk to fetch single product
export const fetchProductById = createAsyncThunk(
  "products/fetchProductById",
  async (productId: string) => {
    try {
      const response = await axios.get<{ success: boolean; data: Product }>(`/api/product?id=${productId}`);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to fetch product");
    }
  }
);

// Product slice
const productSlice = createSlice({
  name: "products",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch all products
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload.data;
        state.pagination = action.payload.pagination || null;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch products";
      })

    // Fetch new arrivals
    builder
      .addCase(fetchNewArrivals.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNewArrivals.fulfilled, (state, action) => {
        state.loading = false;
        state.newArrivals = action.payload.data;
      })
      .addCase(fetchNewArrivals.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch new arrivals";
      })

    // Fetch trending products
    builder
      .addCase(fetchTrending.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTrending.fulfilled, (state, action) => {
        state.loading = false;
        state.trending = action.payload.data;
      })
      .addCase(fetchTrending.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch trending products";
      })

    // Fetch best sellers
    builder
      .addCase(fetchBestSellers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBestSellers.fulfilled, (state, action) => {
        state.loading = false;
        state.bestSellers = action.payload.data;
      })
      .addCase(fetchBestSellers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch best sellers";
      })

    // Fetch single product
    builder
      .addCase(fetchProductById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductById.fulfilled, (state, action) => {
        state.loading = false;
        // Update the product in the products array if it exists
        const index = state.products.findIndex(p => p._id === action.payload._id);
        if (index !== -1) {
          state.products[index] = action.payload;
        }
      })
      .addCase(fetchProductById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch product";
      });
  },
});

export const { clearError, setLoading } = productSlice.actions;
export default productSlice.reducer;
