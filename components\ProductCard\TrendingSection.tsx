/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import gsap from "gsap";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useEffect, useRef } from "react";
import "swiper/css";
import "swiper/css/navigation";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
const trendingData = [
  {
    id: 1,
    img: "https://www.justwatches.com/cdn/shop/files/VERSACE_10501501-dd8e-43a9-a1b9-94dea266b9e2.jpg?v=1719551363&width=600",
    h2: "Versace",
  },
  {
    id: 2,
    img: "https://www.justwatches.com/cdn/shop/files/GUESS_6e1aa606-2897-4a08-a495-7fd5789dfbeb.jpg?v=1719551326&width=600",
    h2: "Timex - Fria",
  },
  {
    id: 3,
    img: "https://www.justwatches.com/cdn/shop/files/PP---Skull.jpg?v=1719551287&width=600",
    h2: "Timex - Global",
  },
  {
    id: 4,
    img: "https://www.justwatches.com/cdn/shop/files/FRIA---TIMEX.jpg?v=1719551479&width=600",
    h2: "Philippe Peabody - Skull",
  },
  {
    id: 5,
    img: "https://www.justwatches.com/cdn/shop/files/768_x_768_Global.jpg?v=1734587373&width=600",
    h2: "Philippe Peabody - Skull",
  },
];
const TrendingSection = () => {
  const swiperRef = useRef(null);
  useEffect(() => {
    if (swiperRef.current && "swiper" in swiperRef.current) {
      (swiperRef.current as any).swiper.update();
      const onSlideChange = () => {
        gsap.fromTo(
          ".swiper-slide-active",
          { opacity: 0, scale: 0.9 },
          { opacity: 1, scale: 1, duration: 0.5, ease: "power2.out" }
        );
      };

      (swiperRef.current as any).swiper.on(
        "slideChangeTransitionStart",
        onSlideChange
      );

      return () => {
        (swiperRef.current as any).swiper?.off(
          "slideChangeTransitionStart",
          onSlideChange
        );
      };
    }
  }, []);
  return (
    <>
      <div>
        <div className="flex justify-between items-center gap-2 mb-10">
          <h2 className="text-2xl md:text-4xl lg:text-5xl font-semibold">
            Trending Now
          </h2>
          <div className="flex items-center gap-2">
            <button
              id="prev-button2"
              className=" text-gray-900 size-[50px] flex justify-center items-center rounded-full shadow-xl"
            >
              <ChevronLeft size={24} />
            </button>
            <button
              id="next-button2"
              className=" text-gray-900 size-[50px] flex justify-center items-center rounded-full shadow-xl"
            >
              <ChevronRight size={24} />
            </button>
          </div>
        </div>
        <div className="h-auto w-full ">
          <Swiper
            ref={swiperRef}
            spaceBetween={10}
            speed={1000}
            slidesPerView={1}
            loop={true}
            modules={[Navigation]}
            navigation={{
              prevEl: "#prev-button2",
              nextEl: "#next-button2",
            }}
            breakpoints={{
              640: {
                slidesPerView: 1,
                spaceBetween: 10,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 10,
              },
              1280: {
                slidesPerView: 3,
                spaceBetween: 10,
              },
            }}
          >
            {trendingData.map((item, index) => (
              <SwiperSlide key={index}>
                <div>
                  <Image
                    src={item.img}
                    alt={item.h2}
                    width={10000}
                    height={10000}
                    quality={100}
                    className="h-full w-full aspect-square object-cover"
                  />
                  <h2 className="text-sm lg:text-base mt-2 font-medium">
                    {item.h2}
                  </h2>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </>
  );
};

export default TrendingSection;
