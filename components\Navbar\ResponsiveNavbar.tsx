import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Image from "next/image";
import Link from "next/link";
import { CiMail } from "react-icons/ci";
import { IoIosCall } from "react-icons/io";
import megaMenuContent from "./data"; // Import the megaMenuContent data

const ResponsiveNavbar = () => {
  return (
    <div className="w-full">
      <Accordion type="multiple" className="w-full space-y-2">
        <AccordionItem value="special-offers" className="border-b">
          <AccordionTrigger className="py-2 text-sm font-medium hover:text-gray-600">
            <Link href={"/discount"}>Special Offers</Link>
          </AccordionTrigger>
        </AccordionItem>

        <AccordionItem value="brands" className="border-b">
          <AccordionTrigger className="py-2 text-sm font-medium hover:text-gray-600">
            Brands
          </AccordionTrigger>
          <AccordionContent className="py-2 text-sm text-gray-600">
            <ul className="space-y-2">
              <li>
                <Link href="/brands/timex" className="hover:text-blue-600">
                  Timex
                </Link>
              </li>
              <li>
                <Link href="/brands/guess" className="hover:text-blue-600">
                  Guess
                </Link>
              </li>
              <li>
                <Link href="/brands/gc" className="hover:text-blue-600">
                  Gc
                </Link>
              </li>
              {/* Add more brand links as needed */}
            </ul>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="men" className="border-b">
          <AccordionTrigger className="py-2 text-sm font-medium hover:text-gray-600">
            Men
          </AccordionTrigger>
          <AccordionContent className="py-2 text-sm text-gray-600">
            <ul className="space-y-4">
              {megaMenuContent.Men.categories.map((category, index) => (
                <li key={index} className="border-b pb-2">
                  <AccordionItem value={`men-${index}`} className="border-0">
                    <AccordionTrigger className="py-2 text-sm font-medium hover:text-gray-600">
                      {category.title}
                    </AccordionTrigger>
                    <AccordionContent className="py-2 text-sm text-gray-600">
                      <ul className="space-y-2">
                        {category.links.map((link, linkIndex) => (
                          <li key={linkIndex}>
                            <Link
                              href={`/products/${link
                                .toLowerCase()
                                .replace(/\s+/g, "-")}`}
                              className="hover:text-blue-600"
                            >
                              {link}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                </li>
              ))}
            </ul>

            {/* Images Section */}
            <div className="grid grid-cols-2 gap-4 mt-4">
              {megaMenuContent.Men.images.map((image, index) => (
                <div key={index} className="relative w-full h-48">
                  <Link
                    href={`/products/${image.label
                      .toLowerCase()
                      .replace(/\s+/g, "-")}`}
                  >
                    <Image
                      src={image.src}
                      alt={image.alt}
                      fill
                      className="object-cover rounded-md"
                    />
                    <p className="mt-2 text-sm text-center text-gray-600">
                      {image.label}
                    </p>
                  </Link>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="women" className="border-b">
          <AccordionTrigger className="py-2 text-sm font-medium hover:text-gray-600">
            Women
          </AccordionTrigger>
          <AccordionContent className="py-2 text-sm text-gray-600">
            <ul className="space-y-4">
              {megaMenuContent.Women.categories.map((category, index) => (
                <li key={index} className="border-b pb-2">
                  <AccordionItem value={`women-${index}`} className="border-0">
                    <AccordionTrigger className="py-2 text-sm font-medium hover:text-gray-600">
                      {category.title}
                    </AccordionTrigger>
                    <AccordionContent className="py-2 text-sm text-gray-600">
                      <ul className="space-y-2">
                        {category.links.map((link, linkIndex) => (
                          <li key={linkIndex}>
                            <Link
                              href={`/products/${link
                                .toLowerCase()
                                .replace(/\s+/g, "-")}`}
                              className="hover:text-blue-600"
                            >
                              {link}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                </li>
              ))}
            </ul>

            {/* Images Section */}
            <div className="grid grid-cols-2 gap-4 mt-4">
              {megaMenuContent.Women.images.map((image, index) => (
                <div key={index} className="relative w-full h-48">
                  <Link
                    href={`/products/${image.label
                      .toLowerCase()
                      .replace(/\s+/g, "-")}`}
                  >
                    <Image
                      src={image.src}
                      alt={image.alt}
                      fill
                      className="object-cover rounded-md"
                    />
                    <p className="mt-2 text-sm text-center text-gray-600">
                      {image.label}
                    </p>
                  </Link>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="luxury" className="border-b">
          <AccordionTrigger className="py-2 text-sm font-medium hover:text-gray-600">
            Luxury
          </AccordionTrigger>
          <AccordionContent className="py-2 text-sm text-gray-600">
            <ul className="space-y-2">
              <li>
                <Link href="/luxury/brands" className="hover:text-blue-600">
                  Luxury Brands
                </Link>
              </li>
              {/* Add more luxury links as needed */}
            </ul>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="stores" className="border-b">
          <AccordionTrigger className="py-2 text-sm font-medium hover:text-gray-600">
            Stores
          </AccordionTrigger>
          <AccordionContent className="py-2 text-sm text-gray-600">
            Visit our physical and online stores.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="blog" className="border-b">
          <AccordionTrigger className="py-2 text-sm font-medium hover:text-gray-600">
            Blog
          </AccordionTrigger>
          <AccordionContent className="py-2 text-sm text-gray-600">
            Read our latest articles and updates.
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Contact Information at the Bottom */}
      <div className="mt-6 border-t p-6 text-sm text-white rounded-md bg-slate-900">
        <div className="flex items-center gap-2 mb-2">
          <IoIosCall />
          <span>+022-68353125</span>
        </div>
        <div className="flex items-center gap-2">
          <CiMail />
          <a
            href="mailto:<EMAIL>"
            className="hover:text-blue-600"
          >
            <EMAIL>
          </a>
        </div>
      </div>
    </div>
  );
};

export default ResponsiveNavbar;
