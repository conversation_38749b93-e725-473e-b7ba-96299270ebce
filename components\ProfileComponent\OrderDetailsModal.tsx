/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button } from "@/components/ui/button";
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function OrderDetailsModal({
  order,
  onClose,
}: {
  order: any;
  onClose: any;
}) {
  if (!order) return null;

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Order Details</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <p>
            <strong>Order Number:</strong> {order.number}
          </p>
          <p>
            <strong>Order Date:</strong> {order.date}
          </p>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">Shipping Address</h3>
              <p>123 Main St</p>
              <p>Anytown, ST 12345</p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Billing Address</h3>
              <p>123 Main St</p>
              <p>Anytown, ST 12345</p>
            </div>
          </div>

          <h3 className="font-semibold mb-2">Order Items</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">Item Image</TableHead>
                <TableHead>Item Name</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Price</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src="/placeholder.svg"
                    alt="Product"
                    className="h-10 w-10 rounded-full"
                  />
                </TableCell>
                <TableCell>Example Product</TableCell>
                <TableCell>1</TableCell>
                <TableCell>${order.total.toFixed(2)}</TableCell>
              </TableRow>
            </TableBody>
          </Table>

          <div className="flex justify-between items-center">
            <p className="text-xl font-bold">
              Order Total: ${order.total.toFixed(2)}
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button>Download Invoice</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
