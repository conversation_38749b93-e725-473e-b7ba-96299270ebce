/* eslint-disable @next/next/no-img-element */
"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { handleGoogleLogout } from "@/lib/auth";
import Link from "next/link";

export function ProfileDropDown({ img }: { img: string }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <img src={img} alt="User" className="w-8 h-8 rounded-full" />
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <Link
            href={"/profile"}
            className="bg-slate-50 w-full py-3 text-center rounded-md text-black"
          >
            Profile
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <button
            className="bg-red-600 w-full py-2 rounded-md text-white"
            onClick={handleGoogleLogout}
          >
            Logout
          </button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
