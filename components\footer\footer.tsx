"use client";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Facebook, Instagram, Twitter, Youtube } from "lucide-react";
import Link from "next/link";
import LogoComp from "../logo/logo";
const Footer = () => {
  return (
    <footer className="bg-white  border-t">
      <div className="w-full mx-auto grid grid-cols-1 lg:grid-cols-2 gap-10 pt-14 px-10 lg:px-20">
        {/* Newsletter Section */}
        <div className="w-full ">
          <div className="space-y-4  max-w-lg w-full ">
            <Link href={"/"} className=" block">
              <LogoComp />
            </Link>
            <h3 className="font-medium text-lg pt-5">
              SUBSCRIBE TO OUR NEWSLETTER
            </h3>
            <p className="text-sm text-gray-600">
              Be the first to hear about new arrivals, by-invitation-only sales
              and special events
            </p>
            <div className="flex gap-2 items-center border border-black py-2 h-14 overflow-hidden">
              <input
                type="email"
                placeholder="Email"
                className="bg-white focus:border-none border-none focus-within:bottom-0 bottom-2 left-0 right-0 mx-auto w-full px-4 py-2 text-sm border-gray-300 rounded-md focus:ring-0 focus:outline-none"
              />
              <button className="bg-black hover:bg-gray-900 text-white h-14 w-[180px] hover:font-semibold">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Accordion Links Section */}

        {/* Social Links Section */}
        <div className="col-span-1 space-y-10  w-full flex flex-col md:flex-row items-start">
          <div className="space-y-4  mx-auto md:max-w-sm w-full">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="policies">
                <AccordionTrigger>Policies And Order</AccordionTrigger>
                <AccordionContent>
                  <div className="flex flex-col space-y-2">
                    <Link href="#" className="text-sm hover:underline">
                      Authenticity Guarantee
                    </Link>
                    <Link href="#" className="text-sm hover:underline">
                      Privacy Policy
                    </Link>
                    <Link href="#" className="text-sm hover:underline">
                      Return, Exchange & Refund Policy
                    </Link>
                    <Link href="#" className="text-sm hover:underline">
                      Terms and Conditions
                    </Link>
                    <Link href="#" className="text-sm hover:underline">
                      Warranty Information
                    </Link>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="help">
                <AccordionTrigger>Help</AccordionTrigger>
                <AccordionContent>
                  <div className="flex flex-col space-y-2">
                    <Link href="#" className="text-sm hover:underline">
                      {` FAQ's`}
                    </Link>
                    <Link href="#" className="text-sm hover:underline">
                      My Account
                    </Link>
                    <Link href="#" className="text-sm hover:underline">
                      Track my order
                    </Link>
                    <Link href="#" className="text-sm hover:underline">
                      Sitemap
                    </Link>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="company">
                <AccordionTrigger>Company</AccordionTrigger>
                <AccordionContent>
                  <div className="flex flex-col space-y-2">
                    <Link href="#" className="text-sm hover:underline">
                      Blog
                    </Link>
                    <Link href="#" className="text-sm hover:underline">
                      Contact Us
                    </Link>
                    <Link href="#" className="text-sm hover:underline">
                      Our Stores
                    </Link>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
          <div className="w-full">
            <h3 className="font-medium text-lg text-center mb-4">
              Stay connected
            </h3>
            <div className="flex gap-4 justify-center">
              <Link href="#" className="hover:text-gray-600">
                <Instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="hover:text-gray-600">
                <Facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="hover:text-gray-600">
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </Link>
              <Link href="#" className="hover:text-gray-600">
                <Youtube className="h-5 w-5" />
                <span className="sr-only">YouTube</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Copyright Section */}
      <div className=" text-center mt-12 py-8 border-t text-sm text-gray-600">
        <p className="text-xl">
          © 2025, Just Watches, Online Retailer - MiNiAROX LLP
        </p>
      </div>
    </footer>
  );
};

export default Footer;
