"use client";
import gsap from "gsap";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { GoArrowRight } from "react-icons/go";

const productCategory = [
  {
    id: 1,
    title: "Leather Strap",
    img: "https://www.justwatches.com/cdn/shop/files/569_x_679_Leather_Strap.jpg?v=1734587573&width=1080",
  },
  {
    id: 2,
    title: "Automatic Watches",
    img: "https://www.justwatches.com/cdn/shop/files/automatic_7e0dde29-c80b-4be7-a6e6-9b7e0580c5c4.jpg?v=1719562237&width=1080",
  },
  {
    id: 3,
    title: "Choronograph Watches",
    img: "https://www.justwatches.com/cdn/shop/files/chronograph.jpg?v=1719562249&width=1080",
  },
  {
    id: 4,
    title: "Round Dial Watches",
    img: "https://www.justwatches.com/cdn/shop/files/round-dial.jpg?v=1719562328&width=1080",
  },
  {
    id: 5,
    title: "Gold Dial Watches",
    img: "https://www.justwatches.com/cdn/shop/files/gold-dial.jpg?v=1719562269&width=1080",
  },
  {
    id: 6,
    title: "Emerald/Green Dial Watches",
    img: "https://www.justwatches.com/cdn/shop/files/569_x_679_Green_Dial.jpg?v=1734587895&width=1080",
  },
  {
    id: 7,
    title: "Blue Dial Watches",
    img: "https://www.justwatches.com/cdn/shop/files/569_x_679_Blue_Dial.jpg?v=1734588066&width=1080",
  },
];
const ProductTrending = () => {
  const [hoveredImg, setHoveredImg] = useState(productCategory[0].img);
  const imageRef = useRef<HTMLImageElement>(null);
  useEffect(() => {
    if (imageRef.current) {
      gsap.fromTo(
        imageRef.current,
        { opacity: 0, scale: 0.95, clipPath: "inset(0 0 100% 0)" },
        {
          opacity: 1,
          scale: 1,
          duration: 0.7,
          ease: "power2.out",
          clipPath: "inset(0 0 0% 0)",
        }
      );
    }
  }, [hoveredImg]);
  return (
    <div className="px-5 lg:px-10 py-10">
      <div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 place-content-center place-items-center">
          <div className="w-full  mx-auto text-center lg:text-left 2xl:pl-20 py-5">
            <h2 className="drop-shadow-2xl mb-10 font-medium capitalize text-base md:text-xl lg:text-2xl">
              Popular Category
            </h2>
            <ul className="flex flex-col justify-center gap-5 w-full">
              {productCategory.map((item, index) => {
                return (
                  <li
                    key={item.id + index}
                    className="group transition-all ease-linear duration-300 w-full py-2 px-5 flex justify-between items-center border-b border-gray-100 cursor-pointer"
                    onMouseEnter={() => setHoveredImg(item.img)}
                  >
                    <div
                      className="flex items-center gap-2 group-hover:translate-x-10 transition-all ease-linear duration-300 xl:h-[55px]  "
                      data-image={item.img}
                    >
                      <Link
                        href={"/"}
                        className="text-4xl font-light group-hover:font-normal "
                      >
                        {item.title}
                      </Link>
                      <GoArrowRight className="w-11 h-11 opacity-0 group-hover:opacity-100 transition-all ease-linear duration-300 " />
                    </div>
                  </li>
                );
              })}
            </ul>
          </div>
          <div
            className="max-w-[500px] w-full h-full relative"
            style={{ clipPath: "inset(0 0 0% 0)" }}
          >
            <Image
              ref={imageRef}
              src={hoveredImg}
              width={1000}
              height={1000}
              alt="product"
              className="w-full h-full object-cover transition-opacity duration-300"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductTrending;
