import {
  AuthError,
  confirmPasswordReset,
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
  signInWithPopup,
  UserCredential,
} from "firebase/auth";
import { auth, googleProvider } from "./firebase";

export const createUser = async (
  email: string,
  password: string
): Promise<UserCredential | AuthError> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      email,
      password
    );
    return userCredential;
  } catch (error) {
    return error as AuthError;
  }
};

export const signInUser = async (
  email: string,
  password: string
): Promise<UserCredential | AuthError> => {
  try {
    const userCredential = await signInWithEmailAndPassword(
      auth,
      email,
      password
    );
    return userCredential;
  } catch (error) {
    return error as AuthError;
  }
};

export const handleGoogleLogin = async (): Promise<{
  user: UserCredential | null;
  error: Error | null;
}> => {
  const userResult: {
    user: UserCredential | null;
    error: Error | null;
  } = { user: null, error: null };
  try {
    const result = await signInWithPopup(auth, googleProvider);
    userResult.user = result;
  } catch (error) {
    userResult.error =
      error instanceof Error ? error : new Error("Google sign-in failed");
  }
  return userResult;
};

export const handleGoogleLogout = async () => {
  await auth.signOut();
};

export const getUser = async () => {
  return auth.currentUser;
};

export const forgetPassword = async ({ email }: { email: string }) => {
  const result = await sendPasswordResetEmail(auth, email);
  return result;
};
export const confirmPassword = async ({
  oobCode,
  password,
}: {
  oobCode: string;
  password: string;
}) => {
  const result = await confirmPasswordReset(auth, oobCode, password);
  return result;
};
