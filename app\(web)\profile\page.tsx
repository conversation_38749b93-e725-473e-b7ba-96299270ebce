/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import AccountSettings from "@/components/ProfileComponent/AccountSettings";
import OrderDetailsModal from "@/components/ProfileComponent/OrderDetailsModal";
import OrderHistory from "@/components/ProfileComponent/OrderHistory";
import ProfileInformation from "@/components/ProfileComponent/ProfileInformation";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useGetUser";
import { useRouter } from "next/navigation";
import { useState } from "react";
const Profile = () => {
  const { user, loading } = useAuth();
  const [activeSection, setActiveSection] = useState("profile");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);

  const openOrderDetails = (order: any) => {
    setSelectedOrder(order);
    setIsModalOpen(true);
  };
  const router = useRouter();
  if (loading) {
    return <div>Loading...</div>;
  }
  if (!user) {
    router.push("/login");
  }

  console.log(user);

  return (
    <>
      <div className="flex h-max flex-col bg-white max-w-7xl mx-auto  p-10 border my-10  ">
        <aside className="pb-10">
          <nav className="flex  gap-4 items-center flex-row flex-wrap  justify-center    px-10  ">
            <Button
              variant={`${activeSection === "profile" ? "default" : "ghost"}`}
              className="lg:w-[200px] lg:h-[50px] rounded-full "
              onClick={() => setActiveSection("profile")}
            >
              Profile Information
            </Button>
            <Button
              variant={`${activeSection === "orders" ? "default" : "ghost"}`}
              onClick={() => setActiveSection("orders")}
              className="lg:w-[200px] lg:h-[50px] rounded-full "
            >
              Order History
            </Button>
            <Button
              variant={`${activeSection === "settings" ? "default" : "ghost"}`}
              className="lg:w-[200px] lg:h-[50px] rounded-full "
              onClick={() => setActiveSection("settings")}
            >
              Account Settings
            </Button>
          </nav>
        </aside>

        <div className="flex-1 ">
          {activeSection === "profile" && (
            <ProfileInformation
              key={12}
              imgurl={user?.photoURL || "https://placehold.co/600x400.png"}
              name={user?.displayName || "NotFound"}
              email={user?.email || "<EMAIL>"}
            />
          )}
          {activeSection === "orders" && (
            <OrderHistory openOrderDetails={openOrderDetails} />
          )}
          {activeSection === "settings" && <AccountSettings />}
        </div>

        {isModalOpen && (
          <OrderDetailsModal
            order={selectedOrder}
            onClose={() => setIsModalOpen(false)}
          />
        )}
      </div>
    </>
  );
};

export default Profile;
