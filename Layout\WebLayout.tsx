"use client";
import gsap from "gsap";
import { usePathname } from "next/navigation";
import React, { useEffect } from "react";

const WebLayout = ({ children }: { children: React.ReactNode }) => {
  const pathname = usePathname();

  useEffect(() => {
    gsap.fromTo(
      ".page-transition",
      { opacity: 0, scale: 0.5, x: "-10%", y: "-200" }, // Start: Shrunk & moved left
      { opacity: 1, scale: 1, x: 0, y: 0, duration: 0.7, ease: "power2.out" } // End: Normal size & position
    );
  }, [pathname]);

  return <div className="page-transition">{children}</div>;
};

export default WebLayout;
