import ProductSection from "@/components/ProductCard/ProductSection";
import ProductDetailsCard from "@/components/productDetails/ProductDetailsCard";

export default async function DetailsPage({
  params,
}: {
  params: Promise<{ product: string }>;
}) {
  const slug = (await params).product;

  return (
    <>
      <section className="pb-20">
        {slug}
        <ProductDetailsCard />
        <div className="px-5 lg:px-10 py-5">
          <ProductSection title="Related Products " />
        </div>
        <div className="px-5 lg:px-10 py-5">
          <ProductSection title=" Luxury Brand Watches " />
        </div>
      </section>
    </>
  );
}
