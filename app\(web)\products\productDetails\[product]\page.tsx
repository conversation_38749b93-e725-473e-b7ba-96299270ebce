import ProductSection from "@/components/ProductCard/ProductSection";
import ProductDetailsCard from "@/components/productDetails/ProductDetailsCard";
import { notFound } from "next/navigation";

// Product interface
interface Product {
  _id: string;
  name: string;
  shortDescription: string;
  description: string;
  price: number;
  salePrice: number;
  imgUrl: Array<{
    id: string;
    imageName: string;
    url: string;
  }>;
  descriptionImages: {
    image1: string;
    image2: string;
  };
  brand?: string;
  category?: string;
  isSale: boolean;
  isNew?: boolean;
  isTrending?: boolean;
  isHaveCoupon: boolean;
  couponCode?: string;
  stock?: number;
  createdAt: string;
  updatedAt: string;
}

// Fetch product data from API
async function getProduct(productId: string): Promise<Product | null> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
    const response = await fetch(`${baseUrl}/api/product?id=${productId}`, {
      cache: "no-store", // Always fetch fresh data
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();

    if (data.success && data.data) {
      return data.data;
    }

    return null;
  } catch (error) {
    console.error("Error fetching product:", error);
    return null;
  }
}

export default async function DetailsPage({
  params,
}: {
  params: Promise<{ product: string }>;
}) {
  const productId = (await params).product;

  // Fetch the product data
  const product = await getProduct(productId);

  // If product not found, show 404
  if (!product) {
    notFound();
  }

  return (
    <>
      <section className="pb-20">
        <ProductDetailsCard product={product} />
        <div className="px-5 lg:px-10 py-5">
          <ProductSection title="Related Products" type="newArrivals" />
        </div>
        <div className="px-5 lg:px-10 py-5">
          <ProductSection title="Luxury Brand Watches" type="bestSellers" />
        </div>
      </section>
    </>
  );
}
