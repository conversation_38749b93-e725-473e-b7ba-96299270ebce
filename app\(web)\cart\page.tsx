"use client";
import { CartItem } from "@/components/Cart/CartItem";
import { CartSummary } from "@/components/Cart/CartSummary";
import { useState } from "react";
const CheckoutPage = () => {
  const [cartItems, setCartItems] = useState([
    {
      id: 1,
      name: "Versace Dominus Blue Dial Rectangle Case Swiss Quartz Chrono Men Watch",
      price: 118380,
      quantity: 1,
      image:
        "https://www.justwatches.com/cdn/shop/files/VE6H00723.jpg?v=1695373720",
    },
    {
      id: 2,
      name: "Versace Dominus Blue Dial Rectangle Case Swiss Quartz Chrono Men Watch",
      price: 118380,
      quantity: 1,
      image:
        "https://www.justwatches.com/cdn/shop/files/VE6H00723.jpg?v=1695373720",
    },
  ]);
  const handleQuantityChange = (itemId: number, newQuantity: number) => {
    if (newQuantity < 1) return;
    setCartItems((prevItems) =>
      prevItems.map((item) =>
        item.id === itemId ? { ...item, quantity: newQuantity } : item
      )
    );
  };

  const handleRemove = (itemId: number) => {
    setCartItems((prevItems) => prevItems.filter((item) => item.id !== itemId));
  };

  const subtotal = cartItems.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  return (
    <div className="px-5 lg:px-10 py-20 ">
      <h1 className="text-2xl font-bold mb-6">Shopping Cart</h1>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-10">
        <div className="col-span-3">
          <div className="grid grid-cols-3 gap-4 p-4 font-semibold  text-sm md:text-base text-center border-b border-slate-800 ">
            <p>Product</p>
            <p>Price</p>
            <p>Quantity</p>
          </div>
          <div className="flex flex-col gap-5">
            {cartItems.map((item) => (
              <CartItem
                key={item.id}
                name={item.name}
                price={item.price}
                quantity={item.quantity}
                image={item.image}
                onQuantityChange={(q) => handleQuantityChange(item.id, q)}
                onRemove={() => handleRemove(item.id)}
              />
            ))}
          </div>
        </div>

        <div>
          <h2 className="text-lg font-bold mb-6">Total</h2>
          <CartSummary subtotal={subtotal} />
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
