import { configureStore } from "@reduxjs/toolkit";
import cartReducer from "./Features/addToCardSlice";
import productReducer from "./Features/productSlice";

export const makeStore = () => {
  return configureStore({
    reducer: {
      cart: cartReducer,
      products: productReducer,
    },
  });
};

export type AppStore = ReturnType<typeof makeStore>;

export type RootState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];
