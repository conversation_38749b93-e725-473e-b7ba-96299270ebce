"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useEffect, useRef, useState } from "react";

gsap.registerPlugin(ScrollTrigger);

// Function to split text into paragraphs dynamically
const splitTextIntoParagraphs = (text: string): string[] => {
  // Split by double newlines or periods followed by a space/newline to create paragraphs
  return text
    .split(/\n\n|\.\s+/)
    .map((para) => para.trim())
    .filter((para) => para.length > 0)
    .map((para) => para + "."); // Ensure each paragraph ends with a period for consistency
};

export default function PrivacyCard() {
  const [isExpanded, setIsExpanded] = useState(false);
  const sectionRef = useRef(null);
  const headingRef = useRef(null);
  const contentRef = useRef(null);
  const textRefs = useRef<(HTMLParagraphElement | null)[]>([]);

  // Full text content (combined into one string)
  const fullText = `
    LumeAxis has become synonymous with prestige and luxury and has since grown to be the largest independent, MINIAXIS Group-owned watch store in the country. 
    Whether you shop online or at one of our stores, you can trust that we are an Official Authorized Retailer for all the brands we offer. Every watch comes with the Manufacturer’s warranty, complete with all necessary documentation, ensuring 100% authenticity. 
    Every order is fulfilled directly from the LumeAxis warehouse (an Authorized partner managing the JustWatches online store). You can always rely on us for free shipping, hassle-free returns, competitive prices, and exceptional, rapid customer service. 
    If you need any assistance after your purchase, you can contact LumeAxis customer care at 022 68353125 (10 am to 6 pm, Monday to Saturday, except government holidays) or email <NAME_EMAIL>. We are here to assist you.
  `;

  // Split the text into paragraphs dynamically
  const paragraphs = splitTextIntoParagraphs(fullText);

  useEffect(() => {
    const section = sectionRef.current;
    const heading = headingRef.current;
    const firstText = textRefs.current[0];

    gsap.fromTo(
      heading,
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        scrollTrigger: {
          trigger: section,
          start: "top center",
        },
      }
    );

    gsap.fromTo(
      firstText,
      { opacity: 0, y: 30 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        delay: 0.2,
        scrollTrigger: {
          trigger: section,
          start: "top center",
        },
      }
    );
  }, []);

  useEffect(() => {
    const content = contentRef.current;
    const texts = textRefs.current.slice(1); // All paragraphs except the first one

    if (isExpanded) {
      gsap.fromTo(
        texts,
        {
          opacity: 0,
          y: 20,
          display: "block",
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.5,
          stagger: 0.1,
        }
      );
    } else {
      gsap.to(texts, {
        opacity: 0,
        y: 20,
        duration: 0.3,
        onComplete: () => {
          if (content) {
            gsap.set(texts, { display: "none" });
          }
        },
      });
    }
  }, [isExpanded]);

  return (
    <section ref={sectionRef} className="py-16 px-4 bg-white">
      <div className="container mx-auto max-w-4xl space-y-8">
        <h2 ref={headingRef} className="text-4xl font-bold text-center">
          Premium Watches from Just Watches
        </h2>

        <div ref={contentRef} className="space-y-6">
          {paragraphs.map((paragraph, index) => (
            <p
              key={index}
              ref={(el: HTMLParagraphElement | null) => {
                textRefs.current[index] = el;
              }}
              className="text-gray-700"
              style={{
                display: index === 0 || isExpanded ? "block" : "none",
              }}
            >
              {paragraph}
            </p>
          ))}
        </div>

        <Button
          variant="outline"
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-2"
        >
          {isExpanded ? (
            <>
              Read less
              <ChevronUp className="h-4 w-4" />
            </>
          ) : (
            <>
              Read more
              <ChevronDown className="h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </section>
  );
}
