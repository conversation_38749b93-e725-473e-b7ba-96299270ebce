"use client"; // Required for Next.js client-side components

import { gsap } from "gsap";
import React, { useEffect, useRef, useState } from "react";
import { IoChevronDown } from "react-icons/io5";

// Define TypeScript interface for props
interface SelectProps {
  items: string[];
}

// Define TypeScript interface for ref object
interface DropdownRef {
  dropdown: HTMLDivElement | null;
}

const SelectComponent: React.FC<SelectProps> = ({ items }) => {
  const [isActive, setIsActive] = useState<boolean>(false);
  const [content, setContent] = useState<string>("Select Option");
  const dropdownRef = useRef<DropdownRef>({ dropdown: null });
  const menuRef = useRef<HTMLDivElement>(null);

  // GSAP animation setup
  useEffect(() => {
    if (menuRef.current) {
      gsap.set(menuRef.current, {
        opacity: 0,
        scale: 0.8,
        transformOrigin: "top center",
      });
    }
  }, []);

  useEffect(() => {
    if (menuRef.current) {
      if (isActive) {
        gsap.to(menuRef.current, {
          opacity: 1,
          scale: 1,
          zIndex: 120,
          duration: 0.3,
          ease: "power2.out",
        });
      } else {
        gsap.to(menuRef.current, {
          opacity: 0,
          scale: 0.8,
          zIndex: -120,
          duration: 0.2,
          ease: "power2.in",
        });
      }
    }
  }, [isActive]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current.dropdown &&
        !dropdownRef.current.dropdown.contains(event.target as Node)
      ) {
        setIsActive(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  return (
    <button
      className="bg-[#fff] border border-gray-200 rounded-md mt-1 justify-between px-3 py-2 flex items-center gap-8 relative cursor-pointer dropdown w-full "
      onClick={() => setIsActive(!isActive)}
      ref={(el: HTMLButtonElement | null) => {
        dropdownRef.current.dropdown = el as unknown as HTMLDivElement;
      }}
    >
      <p
        className={`${
          content === "Select Option" ? "text-gray-400" : "dark:text-[#abc2d3]"
        }`}
      >
        {content}
      </p>
      <IoChevronDown
        className={`${
          isActive ? "rotate-[180deg]" : "rotate-0"
        } transition-all duration-300 text-gray-600 text-[1.2rem]`}
      />
      <div
        ref={menuRef}
        className="w-full absolute top-12 left-0 right-0 -z-[10] bg-[#fff] rounded-xl flex flex-col overflow-hidden py-1"
        style={{
          boxShadow: "0 15px 40px -15px rgba(0, 0, 0, 0.2)",
        }}
      >
        {items?.map((option: string, index: number) => (
          <p
            className="py-2 px-4 text-left text-gray-800 hover:bg-gray-50 transition-all duration-200"
            key={index}
            onClick={(e: React.MouseEvent<HTMLParagraphElement>) => {
              setContent(e.currentTarget.textContent || "");
              setIsActive(false);
            }}
          >
            {option}
          </p>
        ))}
      </div>
    </button>
  );
};

export default SelectComponent;
