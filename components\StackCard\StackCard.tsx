"use client";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Image from "next/image";
import { useEffect, useRef } from "react";

const quotes = [
  {
    id: 1,
    h2: "Cartier",
    img: "https://www.justwatches.com/cdn/shop/files/Nautica--2780x1600.jpg?v=1719552105",
  },
  {
    id: 2,
    h2: "Rolex",
    img: "https://www.justwatches.com/cdn/shop/files/Qtimex-2780x1600.jpg?v=1719552200",
  },
  {
    id: 3,
    h2: "<PERSON><PERSON>",
    img: "https://www.justwatches.com/cdn/shop/files/1920_X_667_ab2ab381-ae69-4d35-82b5-64553219b4b6.png?v=1734588384",
  },
  {
    id: 4,
    h2: "Breitling ",
    img: "https://images.unsplash.com/photo-1587836374828-4dbafa94cf0e?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  },
];
gsap.registerPlugin(ScrollTrigger);
const StackCard = () => {
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);
  useEffect(() => {
    cardRefs.current.forEach((card) => {
      if (card) {
        gsap.fromTo(
          card,
          { y: 100 },
          {
            y: 0,
            duration: 1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: card,
              start: "top 70%",
              end: "top 30%",
              scrub: true,
            },
          }
        );
      }
    });
  }, []);
  return (
    <div className="min-h-screen h-auto relative">
      {quotes.map((item, index) => {
        return (
          <div
            ref={(el: HTMLDivElement | null) => {
              cardRefs.current[index] = el;
            }}
            key={item.id + index}
            className="sticky top-0  text-white h-[800px]"
          >
            <div className="absolute top-0 w-full h-full ">
              <Image
                src={item.img}
                alt="watch"
                width={10000}
                height={10000}
                quality={100}
                className="h-full w-full aspect-square object-cover"
              />
            </div>
            <div className="absolute top-0  z-10  w-full h-full bg-black/50 flex items-center">
              <div className="px-10  w-full flex  justify-between h-full  items-end pb-[200px]">
                <h2 className="text-5xl 2xl:text-[80px] font-semibold text-center">
                  {item.h2}
                </h2>

                <button className="text-center border-2 px-20 py-4 text-xl hover:bg-white hover:text-black  shadow-2xl shadow-green bg-blue-500/10 backdrop-blur-2xl">
                  Buy Now{" "}
                </button>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default StackCard;
