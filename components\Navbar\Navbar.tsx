"use client";
import {
  Sheet,
  Sheet<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { useAuth } from "@/hooks/useGetUser";
import useSlug from "@/hooks/useSlug";
import { gsap } from "gsap";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { CiMail, CiMenuFries } from "react-icons/ci";
import { IoIosCall } from "react-icons/io";
import "swiper/css";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import LogoComp from "../logo/logo";
import megaMenuContent from "./data";
import MenuItems from "./MenuItems";
import ResponsiveNavbar from "./ResponsiveNavbar";
const menuItems = ["brands", "men", "women", "Luxury"];

const Navbar = () => {
  const [hoveredCategory, setHoveredCategory] = useState("");
  const [isBoxVisible, setIsBoxVisible] = useState(false);
  const boxRef = useRef<HTMLDivElement | null>(null);
  const menuRef = useRef<HTMLDivElement | null>(null);
  let timeoutId: NodeJS.Timeout | null = null;
  const { user } = useAuth();
  useEffect(() => {
    gsap.set(boxRef.current, { autoAlpha: 0, y: -10 });
  }, []);

  // Handle mouse enter on menu items
  const handleMouseEnter = (text: string) => {
    if (timeoutId) clearTimeout(timeoutId); // Cancel any scheduled closing
    // setHoveredText(text);
    setHoveredCategory(text);
    setIsBoxVisible(true);
    gsap.to(boxRef.current, {
      autoAlpha: 1,
      y: 0,
      duration: 0.3,
      ease: "power2.out",
    });
  };

  // Handle mouse leaving the entire navbar or mega menu
  const handleMouseLeave = () => {
    timeoutId = setTimeout(() => {
      // setHoveredText("");
      setIsBoxVisible(false);
      setHoveredCategory("");
      gsap.to(boxRef.current, {
        autoAlpha: 0,
        y: -10,
        duration: 0.3,
        ease: "power2.in",
      });
    }, 300); // Small delay for smooth transitions
  };
  const handleLeave = () => {
    setIsBoxVisible(false);
    setHoveredCategory("");
    if (timeoutId) clearTimeout(timeoutId);
    gsap.to(boxRef.current, {
      autoAlpha: 0,
      y: -10,
      duration: 0.3,
      ease: "power2.in",
    });
  };
  const { toSlug } = useSlug();
  return (
    <header className="relative">
      {/* header top part done  */}
      <div className="flex justify-between items-center gap-10 bg-[#F4F4F4] py-3 px-10">
        <div className="hidden lg:flex items-center gap-2">
          <a href="#" className="flex items-center gap-2">
            <IoIosCall /> <span className="text-xs">+88012345678</span>
          </a>
          <span>|</span>
          <a href="#" className="flex items-center gap-2">
            <CiMail />
            <span className="text-xs font-sans"><EMAIL></span>
          </a>
        </div>
        <div className=" max-w-lg  mx-auto text-left capitalize tracking-[0.03em]">
          <Swiper
            modules={[Autoplay]}
            spaceBetween={50}
            slidesPerView={1}
            autoplay={{
              delay: 7000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            loop={true}
          >
            <SwiperSlide>
              <span className="text-sm font-normal  ">
                NO COST EMI AVAILABLE ON ORDER ABOVE BDT.7,000
              </span>
            </SwiperSlide>
            <SwiperSlide>
              <span className="text-sm font-normal ">
                10% off for our new users, use code-
                <span className="font-bold">WELCOME10</span>
              </span>
            </SwiperSlide>
            <SwiperSlide>
              <span className="text-sm  ">Free Shipping, Ship in 24 hours</span>
            </SwiperSlide>
          </Swiper>
        </div>
        <div className="hidden xl:block"></div>
      </div>
      {/* header top part done  */}
      <nav className="flex items-center justify-between py-3 px-5 lg:px-10  h-20">
        {/* logo part  */}
        <div className="flex  items-center justify-between w-full lg:w-fit">
          <div className="xl:hidden">
            <Sheet>
              <SheetTrigger>
                <CiMenuFries className="size-8" />
              </SheetTrigger>
              <SheetContent side={"left"}>
                <SheetHeader>
                  <SheetTitle>My Account </SheetTitle>
                  <SheetDescription>
                    <ResponsiveNavbar />
                  </SheetDescription>
                </SheetHeader>
              </SheetContent>
            </Sheet>
          </div>
          <div className="  w-full lg:flex-0 lg:w-fit">
            <Link
              href={"/"}
              className="w-full  justify-center lg:justify-start flex lg:block  "
            >
              <LogoComp />
            </Link>
          </div>
        </div>
        {/* logo part  */}
        {/* menu  bar part  */}
        <div ref={menuRef} className="hidden xl:block">
          <ul className="flex uppercase justify-around gap-x-10 items-center font-light">
            <li onMouseEnter={handleLeave}>
              <Link href={"/discount"}>
                <span className="cursor-pointer hover:text-gray-600">
                  Spacial Offers
                </span>
              </Link>
            </li>
            {menuItems.map((item, index) => (
              <li
                key={item + index}
                onMouseEnter={() => handleMouseEnter(item)}
                className="cursor-pointer hover:text-gray-600"
              >
                <Link href={"/"}>{item}</Link>
              </li>
            ))}
            <li onMouseEnter={handleLeave}>
              <Link href={"/brands"}>
                <span className=" cursor-pointer  hover:text-gray-600">
                  Stores
                </span>
              </Link>
            </li>
          </ul>
        </div>
        {/* menu  bar part  */}
        {/* search bar part  */}
        <div className="">
          <MenuItems user={user} />
        </div>
        {/* search bar part  */}
      </nav>
      {/*  bar */}
      {isBoxVisible && (
        <div
          ref={boxRef}
          className="absolute min-h-[400px] h-auto border left-0 right-0 bg-white w-full top-[120px] z-[100] flex justify-center items-center shadow-lg"
          onMouseEnter={() => setIsBoxVisible(true)} // Keeps menu open when inside
          onMouseLeave={handleMouseLeave} // Hides when user leaves
        >
          <div className="container mx-auto grid grid-cols-5 gap-6 p-6 ">
            {hoveredCategory === "men" || hoveredCategory === "women" ? (
              <>
                {/* Categories Section */}
                {megaMenuContent[
                  hoveredCategory === "men" ? "Men" : "Women"
                ].categories.map((category, index) => (
                  <div key={index} className="flex flex-col gap-4">
                    <h3 className="font-semibold text-lg">{category.title}</h3>
                    <ul className="space-y-2">
                      {category.links.map((link, linkIndex) => (
                        <li
                          key={linkIndex}
                          className="text-sm hover:text-blue-600 cursor-pointer"
                        >
                          <Link href={`/products/${toSlug(link)}`}>{link}</Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}

                {/* Images Section */}
                {megaMenuContent[
                  hoveredCategory === "men" ? "Men" : "Women"
                ].images.map((image, index) => (
                  <div key={index} className="col-span-1">
                    <Link href={`/products/${toSlug(image.label)}`}>
                      <div className="relative h-48 w-full">
                        <Image
                          src={image.src}
                          alt={image.alt}
                          fill
                          className="object-cover rounded-md"
                        />
                      </div>
                      <p className="mt-2 text-sm text-center">{image.label}</p>
                    </Link>
                  </div>
                ))}
              </>
            ) : hoveredCategory === "brands" || hoveredCategory === "Luxury" ? (
              <div className="col-span-5 grid grid-cols-5 gap-6">
                {megaMenuContent[
                  hoveredCategory === "brands" ? "Brand" : "Luxury"
                ].ALL.map((item, index) => (
                  <Link key={index} href={`/products/${toSlug(item.label)}`}>
                    <div className="group cursor-pointer">
                      <div className="relative h-auto aspect-square w-full overflow-hidden">
                        <Image
                          src={item.src}
                          alt={item.alt}
                          width={1000}
                          height={1000}
                          className="object-cover  w-full h-full rounded-md transition-transform duration-300 group-hover:scale-105"
                        />
                      </div>
                      <div className="mt-3 text-center">
                        <h3 className="text-sm font-medium text-gray-900 group-hover:text-blue-600">
                          {item.label}
                        </h3>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="col-span-5 text-center">{}</div>
            )}
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
