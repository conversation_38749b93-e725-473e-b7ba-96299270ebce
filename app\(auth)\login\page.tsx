"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/hooks/useGetUser";
import { handleGoogleLogin, signInUser } from "@/lib/auth";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { FcGoogle } from "react-icons/fc";

type Inputs = {
  email: string;
  password: string;
};

const LoginPage = () => {
  const { user, loading } = useAuth({ redirectIfUnauthenticated: true }); // Use the custom hook
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<Inputs>();

  const router = useRouter();
  const [formLoading, setFormLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const onSubmit: SubmitHandler<Inputs> = async (data) => {
    setFormLoading(true);
    setErrorMessage(null);
    const result = await signInUser(data.email, data.password);
    setFormLoading(false);

    if ("user" in result) {
      router.push("/profile");
    } else {
      setErrorMessage(result.message || "Login failed");
    }
  };

  const onGoogleLogin = async () => {
    setFormLoading(true);
    setErrorMessage(null);
    const result = await handleGoogleLogin();
    setFormLoading(false);

    if (result.user) {
      // Hook will handle redirect to /profile
    } else if (result.error) {
      setErrorMessage(result.error.message);
    }
  };

  if (loading) return <p>Loading...</p>; // Show loading until auth state is resolved
  if (user) router.push("/");
  return (
    <div className="py-20 px-5">
      <form
        className="max-w-xl mx-auto flex flex-col gap-5 border p-10 rounded-md shadow-md"
        onSubmit={handleSubmit(onSubmit)}
      >
        <h2 className="text-4xl text-center uppercase">LogIn</h2>

        {/* Email Field */}
        <div>
          <Label htmlFor="email" className="py-2 block">
            Email
          </Label>
          <Input
            placeholder="Email Or Phone Number"
            {...register("email")}
            className="border border-gray-300 rounded-md py-5 px-3 w-full"
            disabled={loading}
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
          )}
        </div>

        {/* Password Field */}
        <div>
          <Label htmlFor="password" className="py-2 block">
            Password
          </Label>
          <Input
            type="password"
            placeholder="Password"
            {...register("password")}
            className="border border-gray-300 rounded-md py-5 px-3 w-full"
            disabled={loading}
          />
          {errors.password && (
            <p className="text-red-500 text-sm mt-1">
              {errors.password.message}
            </p>
          )}
        </div>

        {/* Links */}
        <div className="flex items-center justify-between">
          <Link href="/register">
            <span className="underline underline-offset-2 hover:font-semibold text-sm">
              Create An Account
            </span>
          </Link>
        </div>

        {/* Email/Password Login Button */}
        <div>
          <button
            type="submit"
            className="bg-black w-full text-white rounded-md py-3 px-5 disabled:opacity-50"
            disabled={formLoading}
          >
            {formLoading ? "Logging in..." : "Login"}
          </button>
          {errorMessage && (
            <p className="text-red-500 text-sm mt-2">{errorMessage}</p>
          )}
        </div>

        {/* Divider */}
        <div className="flex items-center gap-10">
          <hr className="w-full bg-slate-200 h-0.5 border-none" />
          <span>Or</span>
          <hr className="w-full bg-slate-200 h-0.5 border-none" />
        </div>

        {/* Google Login Button */}
        <div>
          <button
            type="button"
            onClick={onGoogleLogin}
            className="bg-white flex justify-center items-center gap-5 font-semibold w-full text-black rounded-md py-3 px-5 border border-black disabled:opacity-50"
            disabled={loading}
          >
            <FcGoogle className="size-7" />
            {loading ? "Signing in..." : "Login With Google"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default LoginPage;
