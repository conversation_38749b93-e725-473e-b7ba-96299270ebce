"use client";
import { addToCart } from "@/Store/Features/addToCardSlice";
import { useAppDispatch } from "@/Store/hooks/reduxHook";
import gsap from "gsap";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

// Product interface
interface Product {
  _id: string;
  name: string;
  price: number;
  salePrice: number;
  description?: string;
  imgUrl: Array<{ url: string }>;
  brand?: string;
  category?: string;
  isSale?: boolean;
  isNew?: boolean;
  isTrending?: boolean;
  stock?: number;
}

interface ProductCardProps {
  product?: Product;
}

const ProductCard = ({ product }: ProductCardProps) => {
  const dispatch = useAppDispatch();

  // Default fallback images
  const defaultImages = [
    "https://www.justwatches.com/cdn/shop/files/NAPNOS302.jpg?v=1693982626&width=3000",
    "https://www.justwatches.com/cdn/shop/files/NAPNOS302-2.jpg?v=1693982626&width=3000",
  ];

  // Use product images or fallback to default
  const images =
    product?.imgUrl && product.imgUrl.length > 0
      ? [
          product.imgUrl[0]?.url || defaultImages[0],
          product.imgUrl[1]?.url || product.imgUrl[0]?.url || defaultImages[1],
        ]
      : defaultImages;
  const [imgSrc, setImgSrc] = useState("");
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    setImgSrc(images[0]);
    const ctx = gsap.context(() => {}, containerRef);
    return () => ctx.revert();
  }, []);

  const handleHoverCard = () => {
    if (imageRef.current) {
      const currentImage = imageRef.current.getAttribute("data-src");

      if (currentImage === images[0]) {
        gsap.fromTo(
          imageRef.current,
          {
            opacity: 1,
            scale: 1,
          },
          {
            opacity: 0,
            scale: 1,
            duration: 0.3,
            ease: "power1.inOut",
            onComplete: () => {
              setImgSrc(images[1]);
              imageRef.current?.setAttribute("data-src", images[1]);
              gsap.fromTo(
                imageRef.current,
                {
                  opacity: 0,
                  scale: 1.4,
                },
                {
                  opacity: 1,
                  scale: 1,
                  duration: 0.3,
                  ease: "power1.inOut",
                }
              );
            },
          }
        );
      }
    }
  };

  const handleLeaveImageCard = () => {
    if (imageRef.current) {
      const currentImage = imageRef.current.getAttribute("data-src");

      if (currentImage === images[1]) {
        gsap.fromTo(
          imageRef.current,
          {
            opacity: 1,
            scale: 1,
          },
          {
            opacity: 0.2,
            scale: 1.4,
            duration: 0.5,
            ease: "power1.inOut",
            onComplete: () => {
              setImgSrc(images[0]);
              imageRef.current?.setAttribute("data-src", images[0]);
              gsap.fromTo(
                imageRef.current,
                {
                  opacity: 1,
                  scale: 1,
                },
                {
                  opacity: 1,
                  scale: 1,
                  duration: 0.5,
                  ease: "power1.inOut",
                }
              );
            },
          }
        );
      }
    }
  };

  // Handle add to cart
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (product) {
      dispatch(
        addToCart({
          ...product,
          quantity: 1,
        })
      );

      // Optional: Show a toast notification
      alert(`${product.name} added to cart!`);
    }
  };

  return (
    <div className="relative">
      <Link
        href={`/products/productDetails/${
          product?._id || Math.floor(Math.random() * 1000)
        }`}
      >
        <figure
          ref={containerRef}
          className="max-w-md mx-auto overflow-hidden group cursor-pointer bg-transparent border-hidden shadow-none"
        >
          <div className="relative h-full max-h-[566px] border-[.4px] overflow-hidden">
            {/* Product badges */}
            <div className="absolute top-2 right-2 z-[20] flex flex-col gap-1">
              {product?.isNew && (
                <div className="bg-green-500 text-white px-2 py-1 text-xs uppercase rounded">
                  NEW
                </div>
              )}
              {product?.isSale && (
                <div className="bg-red-500 text-white px-2 py-1 text-xs uppercase rounded">
                  SALE
                </div>
              )}
              {product?.isTrending && (
                <div className="bg-blue-500 text-white px-2 py-1 text-xs uppercase rounded">
                  TRENDING
                </div>
              )}
            </div>

            <div className="relative w-full h-auto aspect-square">
              <Image
                onMouseEnter={handleHoverCard}
                onMouseLeave={handleLeaveImageCard}
                width={1000}
                height={1000}
                ref={imageRef}
                data-src={images[0]}
                src={imgSrc || images[0]}
                alt={product?.name || "Product Image"}
                className="w-full h-[566px] object-contain"
              />
            </div>
          </div>

          <div className="p-4">
            <h3 className="text-sm font-medium mb-2 line-clamp-2">
              {product?.name || "Product Name"}
            </h3>
            <div className="flex items-center gap-2 mb-2">
              <p className="text-lg font-semibold text-green-600">
                ৳{product?.salePrice?.toFixed(2) || "0.00"}
              </p>
              {product?.price && product.salePrice < product.price && (
                <p className="text-sm text-gray-500 line-through">
                  ৳{product.price.toFixed(2)}
                </p>
              )}
            </div>
            {product?.brand && (
              <p className="text-xs text-gray-600 mb-2">
                Brand: {product.brand}
              </p>
            )}
          </div>
        </figure>
      </Link>

      {/* Add to Cart Button */}
      {product && (
        <button
          onClick={handleAddToCart}
          className="absolute bottom-4 left-4 right-4 bg-black text-white py-2 px-4 rounded-md hover:bg-gray-800 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300"
        >
          Add to Cart
        </button>
      )}
    </div>
  );
};

export default ProductCard;
