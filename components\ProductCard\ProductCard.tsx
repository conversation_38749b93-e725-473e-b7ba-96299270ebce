"use client";
import gsap from "gsap";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
const images = [
  "https://www.justwatches.com/cdn/shop/files/NAPNOS302.jpg?v=1693982626&width=3000",
  "https://www.justwatches.com/cdn/shop/files/NAPNOS302-2.jpg?v=1693982626&width=3000",
];
const ProductCard = () => {
  const [imgSrc, setImgSrc] = useState("");
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    setImgSrc(images[0]);
    const ctx = gsap.context(() => {}, containerRef);
    return () => ctx.revert();
  }, []);

  const handleHoverCard = () => {
    if (imageRef.current) {
      const currentImage = imageRef.current.getAttribute("data-src");

      if (currentImage === images[0]) {
        gsap.fromTo(
          imageRef.current,
          {
            opacity: 1,
            scale: 1,
          },
          {
            opacity: 0,
            scale: 1,
            duration: 0.3,
            ease: "power1.inOut",
            onComplete: () => {
              setImgSrc(images[1]);
              imageRef.current?.setAttribute("data-src", images[1]);
              gsap.fromTo(
                imageRef.current,
                {
                  opacity: 0,
                  scale: 1.4,
                },
                {
                  opacity: 1,
                  scale: 1,
                  duration: 0.3,
                  ease: "power1.inOut",
                }
              );
            },
          }
        );
      }
    }
  };

  const handleLeaveImageCard = () => {
    if (imageRef.current) {
      const currentImage = imageRef.current.getAttribute("data-src");

      if (currentImage === images[1]) {
        gsap.fromTo(
          imageRef.current,
          {
            opacity: 1,
            scale: 1,
          },
          {
            opacity: 0.2,
            scale: 1.4,
            duration: 0.5,
            ease: "power1.inOut",
            onComplete: () => {
              setImgSrc(images[0]);
              imageRef.current?.setAttribute("data-src", images[0]);
              gsap.fromTo(
                imageRef.current,
                {
                  opacity: 1,
                  scale: 1,
                },
                {
                  opacity: 1,
                  scale: 1,
                  duration: 0.5,
                  ease: "power1.inOut",
                }
              );
            },
          }
        );
      }
    }
  };

  return (
    <Link href={`/products/productDetails/${Math.floor(Math.random() * 1000)}`}>
      <figure
        ref={containerRef}
        className="max-w-md mx-auto overflow-hidden group cursor-pointer bg-transparent border-hidden shadow-none"
      >
        <div className="relative h-full max-h-[566px] border-[.4px]  overflow-hidden">
          <div className="absolute top-2 right-2 z-[20] text-black px-2 py-1 text-lg uppercase">
            NEW
          </div>
          <div className="relative w-full h-auto  aspect-square ">
            <Image
              onMouseEnter={handleHoverCard}
              onMouseLeave={handleLeaveImageCard}
              width={1000}
              height={1000}
              ref={imageRef}
              data-src={images[0]}
              src={imgSrc || images[0]}
              alt="Guess Mist Women Watch"
              className="w-full h-[566px] object-contain "
            />
          </div>
        </div>
        <div className="p-4">
          <h3 className="text-sm font-medium mb-2">
            Guess Mist Women Round Dial Quartz Analog Watch
          </h3>
          <p className="text-lg font-semibold">$17,995</p>
        </div>
      </figure>
    </Link>
  );
};

export default ProductCard;
