"use client";
import Image from "next/image";
import "swiper/css";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
const HeroSection = () => {
  return (
    <>
      <div className="h-auto w-full ">
        <Swiper
          modules={[Autoplay]}
          spaceBetween={50}
          slidesPerView={1}
          autoplay={{
            delay: 3000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          loop={true}
        >
          <SwiperSlide>
            <div>
              <Image
                alt="img-1"
                width={3000}
                height={3000}
                src="https://cdn.shopify.com/s/files/1/0046/3454/2129/collections/Green_Monday_TIMEX-1920_x_822-upload.jpg?v=1738911262"
                className="object-cover w-full h-auto aspect-video max-h-[700px]"
              />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div>
              <Image
                alt="img-1"
                width={3000}
                height={3000}
                src="https://www.justwatches.com/cdn/shop/files/1920x667_Desktop_1_1.png?v=1732513558"
                className="object-cover w-full h-auto aspect-video max-h-[700px]"
              />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div>
              <Image
                alt="img-1"
                width={3000}
                height={3000}
                src="https://www.justwatches.com/cdn/shop/files/1920_x_667px_01b17caf-357f-4979-b9c6-0d489bdc9cda.jpg?v=1739008724"
                className="object-cover w-full h-auto aspect-video max-h-[700px]"
              />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div>
              <Image
                alt="img-1"
                width={3000}
                height={3000}
                src="https://www.justwatches.com/cdn/shop/files/Image_4.jpg?v=1739161311"
                className="object-cover w-full h-auto aspect-video max-h-[700px]"
              />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div>
              <Image
                alt="img-1"
                width={3000}
                height={3000}
                src="https://www.justwatches.com/cdn/shop/files/1920_x_667px_603d5459-4a8a-4dd7-8928-95ba2e06df0f.jpg?v=1739008835"
                className="object-cover w-full h-auto aspect-video max-h-[700px]"
              />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div>
              <Image
                alt="img-1"
                width={3000}
                height={3000}
                src="https://www.justwatches.com/cdn/shop/files/1920_x_667px_bbbc16d0-484c-499d-bc0c-d06a0d2d004d.jpg?v=1739009157"
                className="object-cover w-full h-auto aspect-video max-h-[700px]"
              />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div>
              <Image
                alt="img-1"
                width={3000}
                height={3000}
                src="https://www.justwatches.com/cdn/shop/files/1920_x_667px_01b17caf-357f-4979-b9c6-0d489bdc9cda.jpg?v=1739008724"
                className="object-cover w-full h-auto aspect-video max-h-[700px]"
              />
            </div>
          </SwiperSlide>
        </Swiper>
      </div>
    </>
  );
};

export default HeroSection;
