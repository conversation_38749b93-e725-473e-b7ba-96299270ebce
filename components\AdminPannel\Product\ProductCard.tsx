"use client";
import Image from "next/image";
import Link from "next/link";

// Define the product interface to match our API
interface Product {
  _id: string;
  name: string;
  shortDescription: string;
  description: string;
  price: number;
  salePrice: number;
  imgUrl: Array<{
    id: string;
    imageName: string;
    url: string;
  }>;
  descriptionImages: {
    image1: string;
    image2: string;
  };
  isSale: boolean;
  isHaveCoupon: boolean;
  couponCode?: string;
  createdAt: string;
  updatedAt: string;
}

const ProductCard = ({ product }: { product: Product }) => {
  if (!product) {
    return <div className="text-gray-500">No product found</div>;
  }

  // Get the first image URL or fallback
  const imageUrl =
    product.imgUrl && product.imgUrl.length > 0
      ? product.imgUrl[0].url
      : "/placeholder-image.jpg";

  // Format price
  const formatPrice = (price: number) => `$${price.toFixed(2)}`;

  return (
    <Link href={`/admin/products/${product._id}/edit`}>
      <div className="group relative bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
        <div className="aspect-square w-full bg-gray-200 overflow-hidden">
          <Image
            width={400}
            height={400}
            alt={product.name}
            src={imageUrl}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/placeholder-image.jpg";
            }}
          />
        </div>

        <div className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-medium text-gray-900 truncate">
                {product.name}
              </h3>
              <p className="mt-1 text-xs text-gray-500 line-clamp-2">
                {product.shortDescription}
              </p>
            </div>
          </div>

          <div className="mt-3 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {product.isSale ? (
                <>
                  <span className="text-sm font-medium text-green-600">
                    {formatPrice(product.salePrice)}
                  </span>
                  <span className="text-xs text-gray-500 line-through">
                    {formatPrice(product.price)}
                  </span>
                </>
              ) : (
                <span className="text-sm font-medium text-gray-900">
                  {formatPrice(product.price)}
                </span>
              )}
            </div>

            <div className="flex items-center space-x-1">
              {product.isSale && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Sale
                </span>
              )}
              {product.isHaveCoupon && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Coupon
                </span>
              )}
            </div>
          </div>

          <div className="mt-3 text-xs text-gray-400">
            Created: {new Date(product.createdAt).toLocaleDateString()}
          </div>
        </div>

        {/* Hover overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <span className="bg-white text-gray-900 px-4 py-2 rounded-md text-sm font-medium shadow-lg">
              Click to Edit
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ProductCard;
